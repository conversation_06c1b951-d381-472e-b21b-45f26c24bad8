/**
 * 测试前端表单字段映射
 * 验证HTML表单字段是否正确映射到配置API
 */

const axios = require('axios');

const API_BASE = 'http://localhost:6005/admin_api/wechat';

async function testFrontendFormMapping() {
    console.log('🧪 开始测试前端表单字段映射...\n');

    try {
        // 1. 获取当前配置
        console.log('📖 1. 获取当前配置...');
        const configResponse = await axios.get(`${API_BASE}/config`);
        const config = configResponse.data.data;
        
        console.log('✅ 配置获取成功');

        // 2. 验证关键字段映射
        console.log('\n🔍 2. 验证关键字段映射...');
        
        const expectedFields = {
            'AI_AUTO_REPLY.private_chat.reply_probability': config.AI_AUTO_REPLY?.private_chat?.reply_probability,
            'AI_AUTO_REPLY.group_chat.reply_probability': config.AI_AUTO_REPLY?.group_chat?.reply_probability,
            'AI_AUTO_REPLY.private_chat.assistant_id': config.AI_AUTO_REPLY?.private_chat?.assistant_id,
            'AI_AUTO_REPLY.group_chat.assistant_id': config.AI_AUTO_REPLY?.group_chat?.assistant_id,
            'AI_AUTO_REPLY.private_chat.user_id': config.AI_AUTO_REPLY?.private_chat?.user_id,
            'AI_AUTO_REPLY.group_chat.user_id': config.AI_AUTO_REPLY?.group_chat?.user_id,
            'AI_AUTO_REPLY.private_chat.model': config.AI_AUTO_REPLY?.private_chat?.model,
            'AI_AUTO_REPLY.group_chat.model': config.AI_AUTO_REPLY?.group_chat?.model,
            'AI_AUTO_REPLY.private_chat.assistantName': config.AI_AUTO_REPLY?.private_chat?.assistantName,
            'AI_AUTO_REPLY.group_chat.assistantName': config.AI_AUTO_REPLY?.group_chat?.assistantName,
            'AI_AUTO_REPLY.private_chat.agent': config.AI_AUTO_REPLY?.private_chat?.agent,
            'AI_AUTO_REPLY.group_chat.agent': config.AI_AUTO_REPLY?.group_chat?.agent,
            'AI_AUTO_REPLY.private_chat.useragent': config.AI_AUTO_REPLY?.private_chat?.useragent,
            'AI_AUTO_REPLY.private_chat.enabled': config.AI_AUTO_REPLY?.private_chat?.enabled,
            'AI_AUTO_REPLY.group_chat.enabled': config.AI_AUTO_REPLY?.group_chat?.enabled,
            'AI_AUTO_REPLY.private_chat.keyword_must_trigger': config.AI_AUTO_REPLY?.private_chat?.keyword_must_trigger,
            'AI_AUTO_REPLY.group_chat.keyword_must_trigger': config.AI_AUTO_REPLY?.group_chat?.keyword_must_trigger,
            'AI_AUTO_REPLY.private_chat.render_as_image': config.AI_AUTO_REPLY?.private_chat?.render_as_image,
            'AI_AUTO_REPLY.group_chat.render_as_image': config.AI_AUTO_REPLY?.group_chat?.render_as_image,
            'AI_AUTO_REPLY.private_chat.history_count': config.AI_AUTO_REPLY?.private_chat?.history_count,
            'AI_AUTO_REPLY.group_chat.history_count': config.AI_AUTO_REPLY?.group_chat?.history_count
        };

        console.log('📋 关键字段当前值:');
        let allFieldsPresent = true;
        
        for (const [fieldPath, value] of Object.entries(expectedFields)) {
            if (value !== undefined) {
                console.log(`   ✅ ${fieldPath}: ${value}`);
            } else {
                console.log(`   ❌ ${fieldPath}: 未定义`);
                allFieldsPresent = false;
            }
        }

        // 3. 测试数组字段
        console.log('\n🔍 3. 验证数组字段...');
        
        const arrayFields = {
            'AI_AUTO_REPLY.private_chat.trigger_keywords': config.AI_AUTO_REPLY?.private_chat?.trigger_keywords,
            'AI_AUTO_REPLY.group_chat.trigger_keywords': config.AI_AUTO_REPLY?.group_chat?.trigger_keywords,
            'AI_AUTO_REPLY.private_chat.target_users': config.AI_AUTO_REPLY?.private_chat?.target_users,
            'AI_AUTO_REPLY.group_chat.target_users': config.AI_AUTO_REPLY?.group_chat?.target_users
        };

        for (const [fieldPath, value] of Object.entries(arrayFields)) {
            if (Array.isArray(value)) {
                console.log(`   ✅ ${fieldPath}: [${value.join(', ')}] (${value.length} 项)`);
            } else {
                console.log(`   ❌ ${fieldPath}: 不是数组或未定义`);
                allFieldsPresent = false;
            }
        }

        // 4. 检查是否有废弃字段
        console.log('\n🗑️ 4. 检查废弃字段...');
        
        const deprecatedFields = ['chat_config'];
        let hasDeprecatedFields = false;
        
        for (const field of deprecatedFields) {
            if (config.AI_AUTO_REPLY && config.AI_AUTO_REPLY[field] !== undefined) {
                console.log(`   ❌ 发现废弃字段: AI_AUTO_REPLY.${field}`);
                hasDeprecatedFields = true;
            } else {
                console.log(`   ✅ 无废弃字段: AI_AUTO_REPLY.${field}`);
            }
        }

        // 5. 测试配置结构完整性
        console.log('\n🏗️ 5. 验证配置结构完整性...');
        
        const requiredStructure = {
            'WEB_PORT': config.WEB_PORT,
            'LOG_DIR': config.LOG_DIR,
            'AUTO_START_LISTENING': config.AUTO_START_LISTENING,
            'LISTEN_LIST': config.LISTEN_LIST,
            'AI_AUTO_REPLY': config.AI_AUTO_REPLY,
            'AI_AUTO_REPLY.enabled': config.AI_AUTO_REPLY?.enabled,
            'AI_AUTO_REPLY.debug': config.AI_AUTO_REPLY?.debug,
            'AI_AUTO_REPLY.api_url': config.AI_AUTO_REPLY?.api_url,
            'AI_AUTO_REPLY.api_key': config.AI_AUTO_REPLY?.api_key,
            'AI_AUTO_REPLY.type': config.AI_AUTO_REPLY?.type,
            'AI_AUTO_REPLY.private_chat': config.AI_AUTO_REPLY?.private_chat,
            'AI_AUTO_REPLY.group_chat': config.AI_AUTO_REPLY?.group_chat
        };

        let structureComplete = true;
        for (const [fieldPath, value] of Object.entries(requiredStructure)) {
            if (value !== undefined) {
                console.log(`   ✅ ${fieldPath}: 存在`);
            } else {
                console.log(`   ❌ ${fieldPath}: 缺失`);
                structureComplete = false;
            }
        }

        // 6. 最终结果
        console.log('\n🎯 6. 测试结果总结:');
        
        if (allFieldsPresent && !hasDeprecatedFields && structureComplete) {
            console.log('🎉 前端表单字段映射测试通过！');
            console.log('   ✅ 所有关键字段存在且正确');
            console.log('   ✅ 数组字段格式正确');
            console.log('   ✅ 无废弃字段');
            console.log('   ✅ 配置结构完整');
            console.log('\n🚀 前端表单可以正确映射到后端配置！');
        } else {
            console.log('❌ 前端表单字段映射测试失败！');
            if (!allFieldsPresent) {
                console.log('   ❌ 存在缺失的关键字段');
            }
            if (hasDeprecatedFields) {
                console.log('   ❌ 存在废弃字段');
            }
            if (!structureComplete) {
                console.log('   ❌ 配置结构不完整');
            }
        }

        // 7. 显示完整的配置路径供前端参考
        console.log('\n📝 7. 前端表单字段参考:');
        console.log('   私聊配置路径: AI_AUTO_REPLY.private_chat.*');
        console.log('   群聊配置路径: AI_AUTO_REPLY.group_chat.*');
        console.log('   主要字段:');
        console.log('     - reply_probability (回复概率)');
        console.log('     - assistant_id (助手ID)');
        console.log('     - user_id (用户ID)');
        console.log('     - model (模型)');
        console.log('     - assistantName (助手名称)');
        console.log('     - agent (代理名称)');
        console.log('     - useragent (用户代理，仅私聊)');
        console.log('     - enabled (启用状态)');
        console.log('     - keyword_must_trigger (关键词必须触发)');
        console.log('     - render_as_image (渲染为图片)');
        console.log('     - history_count (历史记录数量)');
        console.log('     - trigger_keywords (触发关键词数组)');
        console.log('     - target_users (目标用户数组)');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testFrontendFormMapping();
