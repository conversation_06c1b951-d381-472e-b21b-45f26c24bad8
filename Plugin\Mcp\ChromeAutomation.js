// Plugin/Mcp/ChromeAutomation.js - Chrome浏览器自动化MCP插件
const BaseMcpPlugin = require('./BaseMcpPlugin');

class ChromeAutomationMcp extends BaseMcpPlugin {
    constructor() {
        super();
        this.name = 'ChromeAutomation';
        this.description = 'Chrome浏览器自动化操作工具，支持页面导航、元素操作、截图、搜索等功能';
        this.vcpName = 'ChromeAutomation'; // 对应的VCP插件名称
    }

    // 获取插件参数定义
    getParameters() {
        return {
            type: 'object',
            properties: {
                action: {
                    type: 'string',
                    description: '操作类型',
                    enum: [
                        'start', 'navigate', 'click', 'input', 'gettext',
                        'screenshot', 'search', 'scroll', 'gettitle',
                        'geturl', 'getcontent', 'execute', 'wait', 'close'
                    ]
                },
                url: {
                    type: 'string',
                    description: '目标URL（用于navigate操作）'
                },
                selector: {
                    type: 'string',
                    description: 'CSS选择器（用于元素操作）'
                },
                text: {
                    type: 'string',
                    description: '输入的文本内容（用于input操作）'
                },
                searchUrl: {
                    type: 'string',
                    description: '搜索网站URL（用于search操作）'
                },
                query: {
                    type: 'string',
                    description: '搜索查询内容（用于search操作）'
                },
                searchSelector: {
                    type: 'string',
                    description: '搜索框选择器（用于search操作，可选）',
                    default: 'input[type="search"], input[name="q"], input[name="query"]'
                },
                submitSelector: {
                    type: 'string',
                    description: '提交按钮选择器（用于search操作，可选）',
                    default: 'button[type="submit"], input[type="submit"]'
                },
                direction: {
                    type: 'string',
                    description: '滚动方向（用于scroll操作）',
                    enum: ['up', 'down', 'top', 'bottom'],
                    default: 'down'
                },
                distance: {
                    type: 'number',
                    description: '滚动距离（用于scroll操作）',
                    default: 500
                },
                script: {
                    type: 'string',
                    description: 'JavaScript代码（用于execute操作）'
                },
                timeout: {
                    type: 'number',
                    description: '超时时间（毫秒）',
                    default: 10000
                },
                format: {
                    type: 'string',
                    description: '截图格式（用于screenshot操作）',
                    enum: ['png', 'jpeg'],
                    default: 'png'
                },
                quality: {
                    type: 'number',
                    description: '截图质量（用于screenshot操作，1-100）',
                    minimum: 1,
                    maximum: 100,
                    default: 90
                },
                fullPage: {
                    type: 'boolean',
                    description: '是否全页面截图（用于screenshot操作）',
                    default: true
                },
                filename: {
                    type: 'string',
                    description: '截图文件名（用于screenshot操作，可选）'
                }
            },
            required: ['action']
        };
    }

    // 执行插件
    async execute(args) {
        try {
            // 验证参数
            this.validateArgs(args);

            this.log('info', `执行Chrome自动化操作: ${args.action}`, args);

            // 调用对应的VCP插件
            const result = await this.callVcpPlugin(args);

            // 处理返回结果
            if (result && typeof result === 'object') {
                if (result.success) {
                    this.log('success', `Chrome自动化操作完成: ${args.action}`);

                    // 根据操作类型返回不同的结果格式
                    switch (args.action) {
                        case 'screenshot':
                            return {
                                success: true,
                                action: args.action,
                                message: result.message || '截图完成',
                                screenshot: result.data || null,
                                timestamp: new Date().toISOString()
                            };

                        case 'gettext':
                        case 'gettitle':
                        case 'geturl':
                        case 'execute':
                            return {
                                success: true,
                                action: args.action,
                                message: result.message || '操作完成',
                                data: result.data || null,
                                timestamp: new Date().toISOString()
                            };

                        case 'getcontent':
                            return {
                                success: true,
                                action: args.action,
                                message: result.message || '页面内容获取完成',
                                content: result.data || null,
                                contentLength: result.data ? result.data.length : 0,
                                timestamp: new Date().toISOString()
                            };

                        default:
                            return {
                                success: true,
                                action: args.action,
                                message: result.message || '操作完成',
                                timestamp: new Date().toISOString()
                            };
                    }
                } else {
                    throw new Error(result.error || '操作失败');
                }
            } else {
                throw new Error('VCP插件返回格式错误');
            }

        } catch (error) {
            this.log('error', `Chrome自动化操作失败: ${error.message}`);

            return {
                success: false,
                action: args.action || 'unknown',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    // 验证特定操作的参数
    validateArgs(args) {
        super.validateArgs(args);

        const { action } = args;

        // 根据不同操作验证特定参数
        switch (action) {
            case 'navigate':
                if (!args.url) {
                    throw new Error('navigate操作需要url参数');
                }
                break;

            case 'click':
            case 'gettext':
            case 'wait':
                if (!args.selector) {
                    throw new Error(`${action}操作需要selector参数`);
                }
                break;

            case 'input':
                if (!args.selector || !args.text) {
                    throw new Error('input操作需要selector和text参数');
                }
                break;

            case 'search':
                if (!args.searchUrl || !args.query) {
                    throw new Error('search操作需要searchUrl和query参数');
                }
                break;

            case 'execute':
                if (!args.script) {
                    throw new Error('execute操作需要script参数');
                }
                break;

            case 'scroll':
                if (args.direction && !['up', 'down', 'top', 'bottom'].includes(args.direction)) {
                    throw new Error('scroll操作的direction参数必须是up、down、top或bottom之一');
                }
                break;

            case 'screenshot':
                if (args.format && !['png', 'jpeg'].includes(args.format)) {
                    throw new Error('screenshot操作的format参数必须是png或jpeg');
                }
                if (args.quality && (args.quality < 1 || args.quality > 100)) {
                    throw new Error('screenshot操作的quality参数必须在1-100之间');
                }
                break;
        }
    }
}

module.exports = ChromeAutomationMcp;