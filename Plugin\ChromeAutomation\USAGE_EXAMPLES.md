# Chrome浏览器自动化插件使用示例

本文档提供了Chrome浏览器自动化插件的详细使用示例，包括常见的自动化场景和最佳实践。

## 基础操作示例

### 1. 启动浏览器并访问网页

```json
{
  "action": "start"
}
```

```json
{
  "action": "navigate",
  "url": "https://www.google.com"
}
```

### 2. 获取页面信息

```json
{
  "action": "gettitle"
}
```

```json
{
  "action": "geturl"
}
```

## 搜索操作示例

### Google搜索

```json
{
  "action": "search",
  "searchUrl": "https://www.google.com",
  "query": "chrome automation tutorial",
  "searchSelector": "input[name='q']",
  "submitSelector": "input[type='submit']"
}
```

### 百度搜索

```json
{
  "action": "search",
  "searchUrl": "https://www.baidu.com",
  "query": "浏览器自动化",
  "searchSelector": "input#kw",
  "submitSelector": "input#su"
}
```

### GitHub搜索

```json
{
  "action": "navigate",
  "url": "https://github.com"
}
```

```json
{
  "action": "input",
  "selector": "input[placeholder='Search GitHub']",
  "text": "chrome automation"
}
```

```json
{
  "action": "click",
  "selector": "button[type='submit']"
}
```

## 表单操作示例

### 登录表单填写

```json
{
  "action": "navigate",
  "url": "https://example.com/login"
}
```

```json
{
  "action": "input",
  "selector": "input[name='username']",
  "text": "myusername"
}
```

```json
{
  "action": "input",
  "selector": "input[name='password']",
  "text": "mypassword"
}
```

```json
{
  "action": "click",
  "selector": "button[type='submit']"
}
```

### 等待页面加载

```json
{
  "action": "wait",
  "selector": ".dashboard",
  "timeout": 15000
}
```

## 数据提取示例

### 提取文章标题

```json
{
  "action": "navigate",
  "url": "https://news.ycombinator.com"
}
```

```json
{
  "action": "gettext",
  "selector": ".titleline > a"
}
```

### 提取多个元素（使用JavaScript）

```json
{
  "action": "execute",
  "script": "Array.from(document.querySelectorAll('.titleline > a')).map(el => el.textContent)"
}
```

### 提取页面所有链接

```json
{
  "action": "execute",
  "script": "Array.from(document.querySelectorAll('a')).map(el => ({text: el.textContent, href: el.href}))"
}
```

## 截图示例

### 全页面截图

```json
{
  "action": "screenshot",
  "format": "png",
  "quality": 90,
  "fullPage": true,
  "filename": "full_page.png"
}
```

### 指定区域截图

```json
{
  "action": "screenshot",
  "format": "jpeg",
  "quality": 80,
  "fullPage": false,
  "filename": "viewport.jpg"
}
```

## 页面滚动示例

### 向下滚动

```json
{
  "action": "scroll",
  "direction": "down",
  "distance": 500
}
```

### 滚动到页面底部

```json
{
  "action": "scroll",
  "direction": "bottom"
}
```

### 滚动到页面顶部

```json
{
  "action": "scroll",
  "direction": "top"
}
```

## 高级JavaScript操作示例

### 模拟键盘事件

```json
{
  "action": "execute",
  "script": "document.querySelector('input').dispatchEvent(new KeyboardEvent('keydown', {key: 'Enter'}))"
}
```

### 修改页面元素

```json
{
  "action": "execute",
  "script": "document.querySelector('h1').style.color = 'red'"
}
```

### 获取页面性能信息

```json
{
  "action": "execute",
  "script": "JSON.stringify(performance.timing)"
}
```

### 检查元素是否存在

```json
{
  "action": "execute",
  "script": "document.querySelector('.target-element') !== null"
}
```

## 复杂自动化流程示例

### 电商网站商品搜索

```json
{
  "action": "navigate",
  "url": "https://www.amazon.com"
}
```

```json
{
  "action": "input",
  "selector": "input#twotabsearchtextbox",
  "text": "laptop"
}
```

```json
{
  "action": "click",
  "selector": "input#nav-search-submit-button"
}
```

```json
{
  "action": "wait",
  "selector": "[data-component-type='s-search-result']",
  "timeout": 10000
}
```

```json
{
  "action": "execute",
  "script": "Array.from(document.querySelectorAll('[data-component-type=\"s-search-result\"] h2 a span')).slice(0, 5).map(el => el.textContent)"
}
```

### 社交媒体自动化

```json
{
  "action": "navigate",
  "url": "https://twitter.com"
}
```

```json
{
  "action": "wait",
  "selector": "input[placeholder*='Search']",
  "timeout": 10000
}
```

```json
{
  "action": "input",
  "selector": "input[placeholder*='Search']",
  "text": "#automation"
}
```

```json
{
  "action": "execute",
  "script": "document.querySelector('input[placeholder*=\"Search\"]').dispatchEvent(new KeyboardEvent('keydown', {key: 'Enter'}))"
}
```

## 错误处理和调试

### 检查元素是否可见

```json
{
  "action": "execute",
  "script": "const el = document.querySelector('.target'); el && el.offsetParent !== null"
}
```

### 获取页面错误信息

```json
{
  "action": "execute",
  "script": "window.console.error.toString()"
}
```

### 等待AJAX请求完成

```json
{
  "action": "execute",
  "script": "new Promise(resolve => { const check = () => { if (window.jQuery && jQuery.active === 0) resolve(true); else setTimeout(check, 100); }; check(); })"
}
```

## 最佳实践

### 1. 使用适当的等待时间

```json
{
  "action": "wait",
  "selector": ".dynamic-content",
  "timeout": 15000
}
```

### 2. 优雅的错误处理

```json
{
  "action": "execute",
  "script": "try { return document.querySelector('.target').textContent; } catch(e) { return null; }"
}
```

### 3. 分步骤操作

不要在一个操作中做太多事情，将复杂操作分解为多个简单步骤。

### 4. 使用具体的选择器

优先使用ID和class选择器，避免使用过于复杂的CSS选择器。

### 5. 截图记录

在关键步骤后进行截图，便于调试和验证。

```json
{
  "action": "screenshot",
  "filename": "step_1_login.png"
}
```

## 常见问题解决

### 元素未找到

```json
{
  "action": "execute",
  "script": "document.querySelector('.target') ? 'found' : 'not found'"
}
```

### 页面加载缓慢

增加等待时间或使用JavaScript检查加载状态：

```json
{
  "action": "execute",
  "script": "document.readyState === 'complete'"
}
```

### 动态内容加载

等待特定元素出现：

```json
{
  "action": "wait",
  "selector": ".dynamic-content",
  "timeout": 20000
}
```

## 关闭浏览器

完成所有操作后，记得关闭浏览器：

```json
{
  "action": "close"
}
```