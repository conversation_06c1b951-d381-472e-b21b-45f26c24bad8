/**
 * 最终测试：验证微信适配器配置的完整兼容性
 * 测试前端、后端API和微信适配器Python代码的配置结构一致性
 */

const axios = require('axios');

const API_BASE = 'http://localhost:6005/admin_api/wechat';

async function testCompleteCompatibility() {
    console.log('🧪 开始完整兼容性测试...\n');

    try {
        // 1. 测试配置获取
        console.log('📖 1. 测试配置获取...');
        const configResponse = await axios.get(`${API_BASE}/config`);
        const config = configResponse.data.data;
        
        console.log('✅ 配置获取成功');
        console.log('📋 配置结构验证:');
        console.log('   - WEB_PORT:', config.WEB_PORT);
        console.log('   - AI_AUTO_REPLY.enabled:', config.AI_AUTO_REPLY?.enabled);
        console.log('   - AI_AUTO_REPLY.private_chat.reply_probability:', config.AI_AUTO_REPLY?.private_chat?.reply_probability);
        console.log('   - AI_AUTO_REPLY.group_chat.reply_probability:', config.AI_AUTO_REPLY?.group_chat?.reply_probability);
        console.log('   - AI_AUTO_REPLY.private_chat.assistant_id:', config.AI_AUTO_REPLY?.private_chat?.assistant_id);
        console.log('   - AI_AUTO_REPLY.group_chat.assistant_id:', config.AI_AUTO_REPLY?.group_chat?.assistant_id);

        // 2. 验证必需字段存在
        console.log('\n🔍 2. 验证必需字段存在...');
        const requiredFields = [
            'WEB_PORT',
            'LOG_DIR', 
            'AUTO_START_LISTENING',
            'LISTEN_LIST',
            'AI_AUTO_REPLY'
        ];
        
        const aiRequiredFields = [
            'enabled',
            'debug',
            'api_url',
            'api_key',
            'type',
            'private_chat',
            'group_chat'
        ];

        const chatRequiredFields = [
            'enabled',
            'model',
            'assistantName',
            'agent',
            'assistant_id',
            'user_id',
            'render_as_image',
            'reply_probability',
            'keyword_must_trigger',
            'trigger_keywords',
            'target_users',
            'history_count'
        ];

        let allFieldsPresent = true;

        // 检查根级字段
        requiredFields.forEach(field => {
            if (config[field] === undefined) {
                console.log(`   ❌ 缺少根级字段: ${field}`);
                allFieldsPresent = false;
            } else {
                console.log(`   ✅ 根级字段存在: ${field}`);
            }
        });

        // 检查AI配置字段
        if (config.AI_AUTO_REPLY) {
            aiRequiredFields.forEach(field => {
                if (config.AI_AUTO_REPLY[field] === undefined) {
                    console.log(`   ❌ 缺少AI配置字段: ${field}`);
                    allFieldsPresent = false;
                } else {
                    console.log(`   ✅ AI配置字段存在: ${field}`);
                }
            });

            // 检查私聊配置字段
            if (config.AI_AUTO_REPLY.private_chat) {
                chatRequiredFields.forEach(field => {
                    if (config.AI_AUTO_REPLY.private_chat[field] === undefined) {
                        console.log(`   ❌ 缺少私聊配置字段: ${field}`);
                        allFieldsPresent = false;
                    } else {
                        console.log(`   ✅ 私聊配置字段存在: ${field}`);
                    }
                });
            }

            // 检查群聊配置字段
            if (config.AI_AUTO_REPLY.group_chat) {
                chatRequiredFields.forEach(field => {
                    if (config.AI_AUTO_REPLY.group_chat[field] === undefined) {
                        console.log(`   ❌ 缺少群聊配置字段: ${field}`);
                        allFieldsPresent = false;
                    } else {
                        console.log(`   ✅ 群聊配置字段存在: ${field}`);
                    }
                });
            }
        }

        // 3. 测试配置保存和恢复
        console.log('\n💾 3. 测试配置保存和恢复...');
        const originalPrivateProb = config.AI_AUTO_REPLY.private_chat.reply_probability;
        const originalGroupProb = config.AI_AUTO_REPLY.group_chat.reply_probability;

        // 修改配置
        const testConfig = JSON.parse(JSON.stringify(config));
        testConfig.AI_AUTO_REPLY.private_chat.reply_probability = 0.95;
        testConfig.AI_AUTO_REPLY.group_chat.reply_probability = 0.55;

        // 保存修改后的配置
        await axios.post(`${API_BASE}/config`, testConfig);
        console.log('   ✅ 配置保存成功');

        // 验证保存结果
        const verifyResponse = await axios.get(`${API_BASE}/config`);
        const verifyConfig = verifyResponse.data.data;

        const privateSaved = verifyConfig.AI_AUTO_REPLY.private_chat.reply_probability === 0.95;
        const groupSaved = verifyConfig.AI_AUTO_REPLY.group_chat.reply_probability === 0.55;

        if (privateSaved && groupSaved) {
            console.log('   ✅ 配置修改验证成功');
        } else {
            console.log('   ❌ 配置修改验证失败');
            allFieldsPresent = false;
        }

        // 恢复原始配置
        await axios.post(`${API_BASE}/config`, config);
        console.log('   ✅ 原始配置已恢复');

        // 4. 检查是否有废弃字段
        console.log('\n🗑️ 4. 检查废弃字段...');
        const deprecatedFields = ['chat_config'];
        let hasDeprecatedFields = false;

        deprecatedFields.forEach(field => {
            if (config.AI_AUTO_REPLY && config.AI_AUTO_REPLY[field] !== undefined) {
                console.log(`   ❌ 发现废弃字段: AI_AUTO_REPLY.${field}`);
                hasDeprecatedFields = true;
            } else {
                console.log(`   ✅ 无废弃字段: AI_AUTO_REPLY.${field}`);
            }
        });

        // 5. 最终结果
        console.log('\n🎯 5. 最终测试结果:');
        if (allFieldsPresent && !hasDeprecatedFields) {
            console.log('🎉 完整兼容性测试通过！');
            console.log('   ✅ 所有必需字段存在');
            console.log('   ✅ 配置保存和加载正常');
            console.log('   ✅ 无废弃字段');
            console.log('   ✅ 前端、后端、微信适配器完全兼容');
        } else {
            console.log('❌ 兼容性测试失败！');
            if (!allFieldsPresent) {
                console.log('   ❌ 存在缺失字段');
            }
            if (hasDeprecatedFields) {
                console.log('   ❌ 存在废弃字段');
            }
        }

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testCompleteCompatibility();
