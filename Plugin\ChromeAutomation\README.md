# Chrome浏览器自动化插件

基于chrome-remote-interface的浏览器自动化插件，支持页面导航、元素操作、截图、搜索等功能。

## 功能特性

- **浏览器管理**: 启动/关闭Chrome浏览器，支持无头模式
- **页面导航**: 导航到指定URL，获取页面信息
- **元素操作**: 点击、输入文本、获取元素内容
- **截图功能**: 全页面截图或指定区域截图
- **搜索功能**: 自动搜索网站内容
- **页面滚动**: 支持多种滚动方向和距离
- **脚本执行**: 执行自定义JavaScript代码
- **智能等待**: 等待元素出现或页面加载完成

## 安装依赖

```bash
npm install chrome-remote-interface
```

## 配置说明

复制 `config.env.example` 为 `config.env` 并根据需要修改配置：

```bash
# Chrome浏览器设置
CHROME_HEADLESS=false          # 是否无头模式
CHROME_PATH=                   # Chrome路径，留空自动检测
CHROME_USER_DATA_DIR=          # 用户数据目录
CHROME_WINDOW_SIZE=1920,1080   # 窗口大小
CHROME_DEBUG_PORT=9222         # 调试端口

# 超时设置
CHROME_TIMEOUT=30000           # 操作超时时间(毫秒)
CHROME_WAIT_TIMEOUT=10000      # 等待元素超时时间(毫秒)

# 截图设置
CHROME_SCREENSHOT_PATH=./screenshots  # 截图保存路径

# 调试模式
DEBUG_MODE=false               # 是否启用调试模式
```

## 使用方法

### 支持的操作类型

1. **start** - 启动Chrome浏览器
2. **navigate** - 导航到指定URL
3. **click** - 点击元素
4. **input** - 输入文本
5. **gettext** - 获取元素文本
6. **screenshot** - 截图
7. **search** - 搜索网站
8. **scroll** - 滚动页面
9. **gettitle** - 获取页面标题
10. **geturl** - 获取当前URL
11. **getcontent** - 获取页面内容
12. **execute** - 执行JavaScript代码
13. **wait** - 等待元素出现
14. **close** - 关闭Chrome浏览器

### 使用示例

#### 1. 启动浏览器并导航
```json
{
  "action": "start"
}
```

```json
{
  "action": "navigate",
  "url": "https://www.google.com"
}
```

#### 2. 搜索操作
```json
{
  "action": "search",
  "searchUrl": "https://www.google.com",
  "query": "chrome automation",
  "searchSelector": "input[name='q']",
  "submitSelector": "input[type='submit']"
}
```

#### 3. 元素操作
```json
{
  "action": "click",
  "selector": "button.search-btn"
}
```

```json
{
  "action": "input",
  "selector": "input#username",
  "text": "myusername"
}
```

#### 4. 获取信息
```json
{
  "action": "gettext",
  "selector": "h1.title"
}
```

```json
{
  "action": "gettitle"
}
```

#### 5. 截图
```json
{
  "action": "screenshot",
  "format": "png",
  "quality": 90,
  "fullPage": true,
  "filename": "page_screenshot.png"
}
```

#### 6. 页面滚动
```json
{
  "action": "scroll",
  "direction": "down",
  "distance": 500
}
```

#### 7. 执行JavaScript
```json
{
  "action": "execute",
  "script": "document.querySelector('h1').textContent"
}
```

#### 8. 等待元素
```json
{
  "action": "wait",
  "selector": ".loading-complete",
  "timeout": 15000
}
```

## 返回格式

所有操作都返回JSON格式的结果：

### 成功响应
```json
{
  "success": true,
  "message": "操作完成",
  "data": "返回的数据(如果有)",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 注意事项

1. **Chrome浏览器**: 需要安装Chrome浏览器，插件会自动检测常见安装路径
2. **端口占用**: 确保配置的调试端口未被占用
3. **权限问题**: 在某些系统上可能需要管理员权限启动Chrome
4. **资源清理**: 使用完毕后建议执行close操作关闭浏览器
5. **并发限制**: 同一时间只能运行一个Chrome实例

## 故障排除

### Chrome启动失败
- 检查Chrome路径是否正确
- 确保端口未被占用
- 检查用户数据目录权限

### 元素操作失败
- 确认选择器语法正确
- 增加等待时间
- 检查元素是否在当前页面

### 截图失败
- 确保截图目录存在且有写入权限
- 检查磁盘空间是否充足

## 技术架构

- **chrome-remote-interface**: 与Chrome DevTools Protocol通信
- **Node.js**: 运行环境
- **异步操作**: 支持Promise和async/await
- **错误处理**: 完善的错误捕获和处理机制