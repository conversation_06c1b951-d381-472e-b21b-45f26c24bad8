const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');
const router = express.Router();

// 微信API配置
const WECHAT_API_BASE = process.env.WECHAT_API_BASE || 'http://localhost:7702';
const WECHAT_CONFIG_PATH = path.join(__dirname, '../WeChat/config.json');

// 引入主WebSocket服务器
let webSocketServer = null;

// 初始化WebSocket引用
function initializeWebSocket(wsServer) {
    webSocketServer = wsServer;
}

// 广播消息到WeChat客户端
function broadcastToWeChatClients(data) {
    if (webSocketServer) {
        webSocketServer.broadcast(data, 'WeChat');
    }
}

// 代理请求到微信API
async function proxyToWeChatAPI(endpoint, options = {}) {
    try {
        const response = await axios({
            url: `${WECHAT_API_BASE}${endpoint}`,
            timeout: 10000,
            ...options
        });
        return response.data;
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            throw new Error('微信适配器服务未运行，请先启动微信适配器');
        }
        throw new Error(error.response?.data?.message || error.message || '请求微信API失败');
    }
}

// 1. 获取微信适配器状态（增强版）
router.get('/status', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/status');
        
        // 增强状态信息
        const enhancedStatus = {
            ...response,
            features: {
                message_logging: true,
                real_time_monitoring: true,
                message_sending: true,
                auto_start_listening: true,
                ai_auto_reply: true,
                segmented_sending: true,
                keyword_trigger: true,
                probability_reply: true,
                advanced_config: true
            }
        };
        
        res.json(enhancedStatus);
    } catch (error) {
        console.error('获取微信状态失败:', error);
        res.json({
            success: false,
            error: error.message,
            data: {
                bot_running: false,
                wechat_connected: false,
                robot_name: null,
                listeners: [],
                listeners_count: 0,
                auto_start_enabled: false,
                features: {
                    message_logging: false,
                    real_time_monitoring: false,
                    message_sending: false,
                    auto_start_listening: false,
                    ai_auto_reply: false,
                    segmented_sending: false,
                    keyword_trigger: false,
                    probability_reply: false,
                    advanced_config: false
                }
            }
        });
    }
});

// 2. 获取和保存微信配置（增强版）
router.get('/config', async (req, res) => {
    try {
        const configData = await fs.readFile(WECHAT_CONFIG_PATH, 'utf8');
        const config = JSON.parse(configData);
        
        // 确保有默认的AI配置
        if (!config.AI_AUTO_REPLY) {
            config.AI_AUTO_REPLY = {
                enabled: false,
                debug: false,
                api_url: 'http://localhost:6005/v1/chat/completions',
                api_key: '114514',
                type: 'mcp',
                enable_context: true,
                memory_tracking: true,
                maxContextSize: 20,
                reply_delay: 2000,
                min_message_length: 1,
                exclude_bot_names: ['AI助手', 'self', '雨安', '失语症_2857896171', '机器人'],
                auto_filter_bots: true,
                api_timeout: 300,
                api_retry_count: 3,
                // 新增：科学算法参数配置
                scientific_algorithms: {
                    enabled: true,
                    emotion_analysis: {
                        russell_model: true,  // Russell环状模型
                        pad_model: true,      // PAD情感空间理论
                        plutchik_wheel: true  // Plutchik情感轮盘
                    },
                    stress_analysis: {
                        yerkes_dodson: true,      // Yerkes-Dodson定律
                        gas_theory: true,         // 一般适应综合症
                        lazarus_appraisal: true   // Lazarus认知评价理论
                    },
                    relationship_analysis: {
                        sternberg_theory: true,   // 三元爱情理论
                        levinger_stages: true,    // 关系发展阶段
                        social_penetration: true  // 社交渗透理论
                    },
                    cognition_analysis: {
                        dawkins_memes: true,      // Dawkins模因理论
                        cognitive_load: true,     // 认知负荷理论
                        bandura_theory: true      // Bandura社会认知理论
                    }
                },
                // 私聊模式配置
                private_chat: {
                    enabled: true,
                    model: 'gemini-2.5-pro-free',
                    assistantName: '雨安安',
                    agent: '雨安安',
                    useragent: '静',
                    assistant_id: 2857896171,
                    user_id: 2166683295,
                    render_as_image: false,
                    reply_probability: 0.8,
                    trigger_keywords: [],
                    keyword_must_trigger: false,
                    target_users: [],
                    history_count: 5  // 私聊历史记录数量
                },
                // 群聊模式配置
                group_chat: {
                    enabled: true,
                    model: 'gemini-2.5-pro-free',
                    assistantName: '雨安安',
                    agent: '雨安安',
                    assistant_id: 2857896171,
                    user_id: 2166683295,
                    render_as_image: false,
                    reply_probability: 0.3,
                    trigger_keywords: ['雨安安'],
                    keyword_must_trigger: true,
                    target_users: [],
                    history_count: 10  // 群聊历史记录数量
                },
                // 新的高级分段配置
                segmented_reply: {
                    enabled: true,
                    min_length: 50,
                    max_segments: 5,
                    target_length: 100,
                    min_segment_length: 30,
                    delay: 1.0,
                    separators: {
                        custom: ['###', '---'],
                        sentence_endings: ['。', '！', '？', '.', '!', '?'],
                        clause_separators: ['；', ';', '：', ':'],
                        comma_separators: ['，', ',', '、'],
                        line_separators: ['\n\n', '\n']
                    }
                },
                // 消息缓存配置
                message_cache: {
                    enabled: false,
                    wait_time: 5,
                    max_wait_cycles: 3,
                    debug: false
                }
            };
        }

        // 向后兼容：如果存在旧的分段配置，转换为新格式
        if (config.AI_AUTO_REPLY && !config.AI_AUTO_REPLY.segmented_reply && config.AI_AUTO_REPLY.enable_segmented_reply !== undefined) {
            config.AI_AUTO_REPLY.segmented_reply = {
                enabled: config.AI_AUTO_REPLY.enable_segmented_reply || false,
                min_length: 50,
                max_segments: config.AI_AUTO_REPLY.max_segments || 5,
                target_length: 100,
                min_segment_length: 30,
                delay: config.AI_AUTO_REPLY.segment_delay || 1.0,
                separators: {
                    custom: config.AI_AUTO_REPLY.custom_separators ?
                        config.AI_AUTO_REPLY.custom_separators.split(',').map(s => s.trim()).filter(s => s) : [],
                    sentence_endings: ['。', '！', '？', '.', '!', '?'],
                    clause_separators: ['；', ';', '：', ':'],
                    comma_separators: ['，', ',', '、'],
                    line_separators: ['\n\n', '\n']
                }
            };

            // 删除旧的配置字段
            delete config.AI_AUTO_REPLY.enable_segmented_reply;
            delete config.AI_AUTO_REPLY.max_segments;
            delete config.AI_AUTO_REPLY.punctuation_priority;
            delete config.AI_AUTO_REPLY.custom_separators;
            delete config.AI_AUTO_REPLY.segment_delay;
        }

        // 确保AI配置包含所有必需的字段
        if (config.AI_AUTO_REPLY) {
            const aiConfig = config.AI_AUTO_REPLY;

            // 确保私聊模式配置完整
            if (!aiConfig.private_chat) {
                aiConfig.private_chat = {};
            }
            const privateDefaults = {
                enabled: true,
                model: 'gemini-2.5-pro-free',
                assistantName: '雨安安',
                agent: '雨安安',
                useragent: '静',
                assistant_id: 2857896171,
                user_id: 2166683295,
                render_as_image: false,
                reply_probability: 0.8,
                trigger_keywords: [],
                keyword_must_trigger: false,
                target_users: [],
                history_count: 10
            };
            Object.keys(privateDefaults).forEach(key => {
                if (aiConfig.private_chat[key] === undefined) {
                    aiConfig.private_chat[key] = privateDefaults[key];
                }
            });

            // 确保群聊模式配置完整
            if (!aiConfig.group_chat) {
                aiConfig.group_chat = {};
            }
            const groupDefaults = {
                enabled: true,
                model: 'gemini-2.5-pro-free',
                assistantName: '雨安安',
                agent: '雨安安',
                assistant_id: 2857896171,
                user_id: 2166683295,
                render_as_image: false,
                reply_probability: 0.3,
                trigger_keywords: ['雨安', '@雨安', 'AI', '助手', '机器人'],
                keyword_must_trigger: true,
                target_users: [],
                history_count: 10
            };
            Object.keys(groupDefaults).forEach(key => {
                if (aiConfig.group_chat[key] === undefined) {
                    aiConfig.group_chat[key] = groupDefaults[key];
                }
            });


            // 确保全局配置字段完整
            const globalDefaults = {
                enable_context: true,
                memory_tracking: true,
                maxContextSize: 20,
                api_timeout: 300,
                api_retry_count: 3,
                auto_filter_bots: true,
                exclude_bot_names: ['AI助手', 'self', '雨安', '失语症_2857896171', '机器人'],
                message_cache: {
                    enabled: false,
                    wait_time: 5,
                    max_wait_cycles: 3,
                    debug: false
                },
                scientific_algorithms: {
                    enabled: true,
                    emotion_analysis: {
                        russell_model: true,
                        pad_model: true,
                        plutchik_wheel: true
                    },
                    stress_analysis: {
                        yerkes_dodson: true,
                        gas_theory: true,
                        lazarus_appraisal: true
                    },
                    relationship_analysis: {
                        sternberg_theory: true,
                        levinger_stages: true,
                        social_penetration: true
                    },
                    cognition_analysis: {
                        dawkins_memes: true,
                        cognitive_load: true,
                        bandura_theory: true
                    }
                }
            };
            Object.keys(globalDefaults).forEach(key => {
                if (aiConfig[key] === undefined) {
                    aiConfig[key] = globalDefaults[key];
                }
            });
            
            // 确保消息缓存配置完整
            if (!aiConfig.message_cache) {
                aiConfig.message_cache = globalDefaults.message_cache;
            } else {
                // 确保每个缓存配置都有默认值
                Object.keys(globalDefaults.message_cache).forEach(key => {
                    if (aiConfig.message_cache[key] === undefined) {
                        aiConfig.message_cache[key] = globalDefaults.message_cache[key];
                    }
                });
            }

            // 确保科学算法配置完整
            if (!aiConfig.scientific_algorithms) {
                aiConfig.scientific_algorithms = globalDefaults.scientific_algorithms;
            } else {
                // 确保每个子配置都存在
                Object.keys(globalDefaults.scientific_algorithms).forEach(category => {
                    if (category !== 'enabled') {
                        if (!aiConfig.scientific_algorithms[category]) {
                            aiConfig.scientific_algorithms[category] = globalDefaults.scientific_algorithms[category];
                        } else {
                            // 确保每个算法都有默认值
                            Object.keys(globalDefaults.scientific_algorithms[category]).forEach(algorithm => {
                                if (aiConfig.scientific_algorithms[category][algorithm] === undefined) {
                                    aiConfig.scientific_algorithms[category][algorithm] = globalDefaults.scientific_algorithms[category][algorithm];
                                }
                            });
                        }
                    }
                });
            }
        }

        res.json({ success: true, data: config });
    } catch (error) {
        console.error('读取配置文件失败:', error);
        res.json({ 
            success: false, 
            error: '读取配置文件失败: ' + error.message,
            data: {
                LISTEN_LIST: [],
                WEB_PORT: 7702,
                LOG_DIR: "chat_logs",
                AUTO_START_LISTENING: true,
                AI_AUTO_REPLY: {
                    enabled: false,
                    debug: false,
                    api_url: 'http://localhost:6005/v1/chat/completions',
                    api_key: '114514',
                    model: 'gemini-2.5-pro-free',
                    type: 'mcp',
                    enable_context: true,
                    memory_tracking: true,
                    maxContextSize: 20,
                    render_as_image: false,
                    useragent: 'ai_auto_reply',
                    assistantName: 'AI助手',
                    agent: 'AI助手',
                    assistant_id: 2857896171,
                    user_id: 2166683295,
                    reply_delay: 2000,
                    min_message_length: 1,
                    exclude_bot_names: ['AI助手', 'self', '雨安', '失语症_2857896171', '机器人'],
                    auto_filter_bots: true,
                    api_timeout: 300,
                    api_retry_count: 3,
                    // 新的高级分段配置
                    segmented_reply: {
                        enabled: true,
                        min_length: 50,
                        max_segments: 5,
                        target_length: 100,
                        min_segment_length: 30,
                        delay: 1.0,
                        separators: {
                            custom: ['###', '---'],
                            sentence_endings: ['。', '！', '？', '.', '!', '?'],
                            clause_separators: ['；', ';', '：', ':'],
                            comma_separators: ['，', ',', '、'],
                            line_separators: ['\n\n', '\n'],
                            remove_punctuation: true
                        }
                    },
                    // 消息缓存配置
                    message_cache: {
                        enabled: false,
                        wait_time: 5,
                        max_wait_cycles: 3,
                        debug: false
                    },
                    // 私聊模式配置
                    private_chat: {
                        reply_probability: 0.85,
                        trigger_keywords: ['雨安', 'AI', '助手'],
                        keyword_must_trigger: false,
                        target_users: ['静']
                    },
                    // 群聊模式配置
                    group_chat: {
                        reply_probability: 0.3,
                        trigger_keywords: ['雨安', '@雨安', 'AI', '助手', '机器人'],
                        keyword_must_trigger: true,
                        target_users: []
                    }
                }
            }
        });
    }
});

router.post('/config', async (req, res) => {
    try {
        const newConfig = req.body;

        // 验证配置格式
        if (!newConfig.AI_AUTO_REPLY) {
            return res.status(400).json({
                success: false,
                error: 'AI_AUTO_REPLY 配置缺失'
            });
        }

        const aiConfig = newConfig.AI_AUTO_REPLY;

        // 处理私聊配置的数组字段
        if (aiConfig.private_chat) {
            const privateChatConfig = aiConfig.private_chat;
            if (typeof privateChatConfig.trigger_keywords === 'string') {
                privateChatConfig.trigger_keywords = privateChatConfig.trigger_keywords.split(',').map(s => s.trim()).filter(s => s);
            }
            if (typeof privateChatConfig.target_users === 'string') {
                privateChatConfig.target_users = privateChatConfig.target_users.split(',').map(s => s.trim()).filter(s => s);
            }

            // 确保私聊配置的数值类型正确
            privateChatConfig.reply_probability = parseFloat(privateChatConfig.reply_probability) || 0.8;
            privateChatConfig.history_count = parseInt(privateChatConfig.history_count) || 10;
            privateChatConfig.assistant_id = parseInt(privateChatConfig.assistant_id) || 2857896171;
            privateChatConfig.user_id = parseInt(privateChatConfig.user_id) || 2166683295;

            // 确保私聊配置的布尔类型正确
            privateChatConfig.enabled = privateChatConfig.enabled === true || privateChatConfig.enabled === 'true';
            privateChatConfig.render_as_image = privateChatConfig.render_as_image === true || privateChatConfig.render_as_image === 'true';
            privateChatConfig.keyword_must_trigger = privateChatConfig.keyword_must_trigger === true || privateChatConfig.keyword_must_trigger === 'true';
        }

        // 处理群聊配置的数组字段
        if (aiConfig.group_chat) {
            const groupChatConfig = aiConfig.group_chat;
            if (typeof groupChatConfig.trigger_keywords === 'string') {
                groupChatConfig.trigger_keywords = groupChatConfig.trigger_keywords.split(',').map(s => s.trim()).filter(s => s);
            }
            if (typeof groupChatConfig.target_users === 'string') {
                groupChatConfig.target_users = groupChatConfig.target_users.split(',').map(s => s.trim()).filter(s => s);
            }

            // 确保群聊配置的数值类型正确
            groupChatConfig.reply_probability = parseFloat(groupChatConfig.reply_probability) || 0.3;
            groupChatConfig.history_count = parseInt(groupChatConfig.history_count) || 10;
            groupChatConfig.assistant_id = parseInt(groupChatConfig.assistant_id) || 2857896171;
            groupChatConfig.user_id = parseInt(groupChatConfig.user_id) || 2166683295;

            // 确保群聊配置的布尔类型正确
            groupChatConfig.enabled = groupChatConfig.enabled === true || groupChatConfig.enabled === 'true';
            groupChatConfig.render_as_image = groupChatConfig.render_as_image === true || groupChatConfig.render_as_image === 'true';
            groupChatConfig.keyword_must_trigger = groupChatConfig.keyword_must_trigger === true || groupChatConfig.keyword_must_trigger === 'true';
        }

        // 处理全局数组字段（向后兼容）
        if (typeof aiConfig.exclude_bot_names === 'string') {
            aiConfig.exclude_bot_names = aiConfig.exclude_bot_names.split(',').map(s => s.trim()).filter(s => s);
        }

        // 处理分段发送配置
        if (aiConfig.segmented_reply) {
            const segmentedConfig = aiConfig.segmented_reply;

            // 确保分段发送的数值类型正确
            segmentedConfig.min_length = parseInt(segmentedConfig.min_length) || 50;
            segmentedConfig.max_segments = parseInt(segmentedConfig.max_segments) || 5;
            segmentedConfig.target_length = parseInt(segmentedConfig.target_length) || 100;
            segmentedConfig.min_segment_length = parseInt(segmentedConfig.min_segment_length) || 30;
            segmentedConfig.delay = parseFloat(segmentedConfig.delay) || 1.0;

            // 确保分段发送的布尔类型正确
            segmentedConfig.enabled = segmentedConfig.enabled === true || segmentedConfig.enabled === 'true';

            // 处理分隔符配置
            if (segmentedConfig.separators) {
                const separators = segmentedConfig.separators;
                ['custom', 'sentence_endings', 'clause_separators', 'comma_separators', 'line_separators'].forEach(type => {
                    if (separators[type] && typeof separators[type] === 'string') {
                        separators[type] = separators[type].split(',').map(s => s.trim()).filter(s => s);
                    }
                });

                if (separators.remove_punctuation !== undefined) {
                    separators.remove_punctuation = separators.remove_punctuation === true || separators.remove_punctuation === 'true';
                }
            }
        }

        // 确保全局数值类型正确
        aiConfig.reply_delay = parseInt(aiConfig.reply_delay) || 2000;
        aiConfig.min_message_length = parseInt(aiConfig.min_message_length) || 1;
        aiConfig.maxContextSize = parseInt(aiConfig.maxContextSize) || 20;
        aiConfig.api_timeout = parseInt(aiConfig.api_timeout) || 300;
        aiConfig.api_retry_count = parseInt(aiConfig.api_retry_count) || 3;

        // 确保全局布尔类型正确
        aiConfig.enabled = aiConfig.enabled === true || aiConfig.enabled === 'true';
        aiConfig.debug = aiConfig.debug === true || aiConfig.debug === 'true';
        aiConfig.auto_filter_bots = aiConfig.auto_filter_bots === true || aiConfig.auto_filter_bots === 'true';
        aiConfig.enable_context = aiConfig.enable_context === true || aiConfig.enable_context === 'true';
        aiConfig.memory_tracking = aiConfig.memory_tracking === true || aiConfig.memory_tracking === 'true';

        // 处理消息缓存配置
        if (aiConfig.message_cache) {
            const cacheConfig = aiConfig.message_cache;

            // 确保消息缓存的数值类型正确
            cacheConfig.wait_time = parseInt(cacheConfig.wait_time) || 5;
            cacheConfig.max_wait_cycles = parseInt(cacheConfig.max_wait_cycles) || 3;

            // 确保消息缓存的布尔类型正确
            cacheConfig.enabled = cacheConfig.enabled === true || cacheConfig.enabled === 'true';
            cacheConfig.debug = cacheConfig.debug === true || cacheConfig.debug === 'true';
        }

        // 确保科学算法配置的布尔类型正确
        if (aiConfig.scientific_algorithms) {
            aiConfig.scientific_algorithms.enabled = aiConfig.scientific_algorithms.enabled === true || aiConfig.scientific_algorithms.enabled === 'true';

            ['emotion_analysis', 'stress_analysis', 'relationship_analysis', 'cognition_analysis'].forEach(category => {
                if (aiConfig.scientific_algorithms[category]) {
                    Object.keys(aiConfig.scientific_algorithms[category]).forEach(algorithm => {
                        aiConfig.scientific_algorithms[category][algorithm] = aiConfig.scientific_algorithms[category][algorithm] === true || aiConfig.scientific_algorithms[category][algorithm] === 'true';
                    });
                }
            });
        }
        
        // 保存配置
        await fs.writeFile(WECHAT_CONFIG_PATH, JSON.stringify(newConfig, null, 2), 'utf8');

        // 通知微信适配器重新加载配置
        try {
            const reloadResponse = await proxyToWeChatAPI('/api/config/reload', {
                method: 'POST'
            });
            console.log('微信适配器配置重载结果:', reloadResponse);
        } catch (reloadError) {
            console.warn('通知微信适配器重载配置失败:', reloadError.message);
        }

        // 广播配置更新
        broadcastToWeChatClients({
            type: 'config_updated',
            data: newConfig
        });

        res.json({ success: true, message: '微信适配器配置已保存并重新加载' });
    } catch (error) {
        console.error('保存配置失败:', error);
        res.status(500).json({ success: false, error: '保存配置失败: ' + error.message });
    }
});

// 3. AI自动回复管理
router.get('/ai/config', async (req, res) => {
    try {
        const configData = await fs.readFile(WECHAT_CONFIG_PATH, 'utf8');
        const config = JSON.parse(configData);
        const aiConfig = config.AI_AUTO_REPLY || {};
        
        res.json({ success: true, data: aiConfig });
    } catch (error) {
        console.error('获取AI配置失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.post('/ai/config', async (req, res) => {
    try {
        const aiConfig = req.body;
        
        // 读取当前配置
        const configData = await fs.readFile(WECHAT_CONFIG_PATH, 'utf8');
        const config = JSON.parse(configData);
        
        // 更新AI配置
        config.AI_AUTO_REPLY = aiConfig;
        
        // 保存配置
        await fs.writeFile(WECHAT_CONFIG_PATH, JSON.stringify(config, null, 2), 'utf8');

        // 通知微信适配器重新加载配置
        try {
            const reloadResponse = await proxyToWeChatAPI('/api/config/reload', {
                method: 'POST'
            });
            console.log('微信适配器AI配置重载结果:', reloadResponse);
        } catch (reloadError) {
            console.warn('通知微信适配器重载AI配置失败:', reloadError.message);
        }

        // 广播配置更新
        broadcastToWeChatClients({
            type: 'ai_config_updated',
            data: aiConfig
        });

        res.json({ success: true, message: 'AI自动回复配置已更新并重新加载' });
    } catch (error) {
        console.error('更新AI配置失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 4. 测试AI回复
router.post('/ai/test', async (req, res) => {
    try {
        const { message, user_name } = req.body;
        
        if (!message || !user_name) {
            return res.status(400).json({ 
                success: false, 
                error: '缺少必需参数 message 或 user_name' 
            });
        }
        
        // 读取AI配置
        const configData = await fs.readFile(WECHAT_CONFIG_PATH, 'utf8');
        const config = JSON.parse(configData);
        const aiConfig = config.AI_AUTO_REPLY || {};
        
        if (!aiConfig.enabled) {
            return res.json({ 
                success: false, 
                error: 'AI自动回复未启用' 
            });
        }
        
        // 调用微信API测试AI回复
        const testData = {
            type: 'ai_test',
            user_name,
            message,
            ai_config: aiConfig
        };
        
        const response = await proxyToWeChatAPI('/api/ai/test', {
            method: 'POST',
            data: testData
        });
        
        res.json({ success: true, data: response });
    } catch (error) {
        console.error('测试AI回复失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 5. 分段发送测试
router.post('/message/send-segmented', async (req, res) => {
    try {
        const { 
            user_name, 
            message, 
            max_segments = 5, 
            punctuation_priority = true,
            custom_separators = '',
            segment_delay = 1000 
        } = req.body;
        
        if (!user_name || !message) {
            return res.status(400).json({ 
                success: false, 
                error: '缺少必需参数 user_name 或 message' 
            });
        }
        
        // 智能分段发送逻辑
        const segments = splitMessageIntoSegments(message, max_segments, punctuation_priority, custom_separators);
        
        // 发送各个分段
        for (let i = 0; i < segments.length; i++) {
            if (i > 0) {
                // 分段间延迟，模拟人类打字间隔
                const actualDelay = segment_delay + Math.random() * 500; // 添加随机延迟
                await new Promise(resolve => setTimeout(resolve, actualDelay));
            }
            
            await proxyToWeChatAPI('/api/message/send', {
                method: 'POST',
                data: {
                    user_name,
                    message: segments[i]
                }
            });
        }
        
        // 广播发送事件
        broadcastToWeChatClients({
            type: 'segmented_message_sent',
            data: { 
                user_name, 
                original_message: message,
                segments_count: segments.length,
                segments: segments,
                timestamp: new Date().toISOString() 
            }
        });
        
        res.json({ 
            success: true, 
            message: '智能分段消息发送成功',
            data: {
                segments_count: segments.length,
                segments_preview: segments.map((seg, idx) => `第${idx + 1}段: ${seg.length > 30 ? seg.substring(0, 30) + '...' : seg}`),
                full_segments: segments
            }
        });
    } catch (error) {
        console.error('分段发送失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 辅助函数：智能分割消息 - 支持自定义分段符号和优先级分段
function splitMessageIntoSegments(message, maxSegments = 5, punctuationPriority = true, customSeparators = '') {
    if (!message || message.trim().length === 0) {
        return [''];
    }

    // 如果消息很短，直接返回
    if (message.length <= 50) {
        return [message];
    }

    const segments = [];
    let text = message.trim();

    if (punctuationPriority) {
        // 构建分段符号列表，优先级从高到低
        const separators = [];
        
        // 用户自定义分段符号（最高优先级）
        if (customSeparators) {
            const customList = customSeparators.split(',').map(sep => sep.trim()).filter(sep => sep);
            separators.push(...customList);
        }
        
        // 默认分段符号（按优先级）
        const defaultSeparators = [
            '。', '！', '？', '.', '!', '?',  // 句末标点（最高优先级）
            '；', ';',                        // 分号
            '，', ',',                        // 逗号
            '：', ':',                        // 冒号
            '、',                             // 顿号
            '\n\n',                          // 双换行
            '\n'                             // 单换行
        ];
        separators.push(...defaultSeparators);
        
        // 按优先级尝试分段
        for (const separator of separators) {
            if (segments.length > 0) {
                break;
            }
                
            if (text.includes(separator)) {
                const parts = text.split(separator);
                if (parts.length > 1) {
                    let tempSegments = [];
                    let currentSeg = '';
                    
                    for (let i = 0; i < parts.length; i++) {
                        if (i === parts.length - 1) {
                            // 最后一部分
                            currentSeg += parts[i];
                        } else {
                            // 非最后部分，加上分隔符
                            currentSeg += parts[i] + separator;
                        }
                        
                        // 检查是否应该结束当前段落
                        if (tempSegments.length < maxSegments - 1 && 
                            currentSeg.length > 30 && 
                            i < parts.length - 1) {  // 不是最后一个部分
                            tempSegments.push(currentSeg.trim());
                            currentSeg = '';
                        }
                    }
                    
                    // 处理剩余内容
                    if (currentSeg.trim()) {
                        tempSegments.push(currentSeg.trim());
                    }
                    
                    // 如果分段数量合适，使用这个结果
                    if (tempSegments.length > 1 && tempSegments.length <= maxSegments) {
                        return tempSegments;
                    }
                }
            }
        }
    }

    // 如果按标点符号分段失败，使用长度分段
    return fallbackLengthSplit(text, maxSegments);
}

// 后备的长度分段方法
function fallbackLengthSplit(text, maxSegments) {
    if (text.length <= 100) {
        return [text];
    }
        
    // 计算平均分段长度
    const avgLength = Math.ceil(text.length / Math.min(maxSegments, 3));
    const minLength = 50;
    const maxLength = avgLength * 2;
    
    const segments = [];
    let remaining = text;
    
    while (remaining && segments.length < maxSegments) {
        if (remaining.length <= maxLength || segments.length === maxSegments - 1) {
            // 最后一段或剩余内容不长
            segments.push(remaining);
            break;
        }
        
        // 寻找合适的分割点
        let cutPoint = avgLength;
        let bestCut = cutPoint;
        
        // 在目标长度附近寻找合适的分割点
        const searchRange = Math.min(50, avgLength / 2);
        for (let i = Math.max(minLength, cutPoint - searchRange); 
             i < Math.min(remaining.length, cutPoint + searchRange); i++) {
            const char = remaining[i];
            if (' \n\t，。！？；：'.includes(char)) {
                bestCut = i + 1;
                break;
            }
        }
        
        segments.push(remaining.substring(0, bestCut).trim());
        remaining = remaining.substring(bestCut).trim();
    }
    
    return segments.filter(seg => seg.trim().length > 0) || [text];
}

// 3. 控制机器人启动/停止
router.post('/bot/start', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/bot/start', { method: 'POST' });

        // 广播状态变化
        broadcastToWeChatClients({
            type: 'bot_status_changed',
            data: { status: 'started' }
        });

        // 处理响应格式
        if (response.success !== undefined) {
            res.json(response);
        } else {
            res.json({ success: true, data: response });
        }
    } catch (error) {
        console.error('启动微信机器人失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.post('/bot/stop', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/bot/stop', { method: 'POST' });

        // 广播状态变化
        broadcastToWeChatClients({
            type: 'bot_status_changed',
            data: { status: 'stopped' }
        });

        // 处理响应格式
        if (response.success !== undefined) {
            res.json(response);
        } else {
            res.json({ success: true, data: response });
        }
    } catch (error) {
        console.error('停止微信机器人失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 4. 发送消息
router.post('/message/send', async (req, res) => {
    try {
        const data = await proxyToWeChatAPI('/api/message/send', {
            method: 'POST',
            data: req.body
        });
        
        // 广播消息发送事件
        broadcastToWeChatClients({
            type: 'message_sent',
            data: { ...req.body, timestamp: new Date().toISOString() }
        });
        
        res.json({ success: true, data });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 5. 发送文件
router.post('/file/send', async (req, res) => {
    try {
        const data = await proxyToWeChatAPI('/api/file/send', {
            method: 'POST',
            data: req.body
        });
        
        // 广播文件发送事件
        broadcastToWeChatClients({
            type: 'file_sent',
            data: { ...req.body, timestamp: new Date().toISOString() }
        });
        
        res.json({ success: true, data });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 6. 监听管理
router.get('/listeners', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/listeners');
        if (response.success !== undefined) {
            res.json(response);
        } else {
            res.json({ success: true, data: response });
        }
    } catch (error) {
        console.error('获取监听列表失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.get('/listeners/status', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/listeners/status');
        if (response.success !== undefined) {
            res.json(response);
        } else {
            res.json({ success: true, data: response });
        }
    } catch (error) {
        console.error('获取监听状态失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.post('/listeners/add', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/listeners/add', {
            method: 'POST',
            data: req.body
        });

        // 广播监听列表变化
        broadcastToWeChatClients({
            type: 'listeners_changed',
            data: { action: 'add', listener: req.body }
        });

        if (response.success !== undefined) {
            res.json(response);
        } else {
            res.json({ success: true, data: response });
        }
    } catch (error) {
        console.error('添加监听失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.post('/listeners/remove', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/listeners/remove', {
            method: 'POST',
            data: req.body
        });

        // 广播监听列表变化
        broadcastToWeChatClients({
            type: 'listeners_changed',
            data: { action: 'remove', listener: req.body }
        });

        if (response.success !== undefined) {
            res.json(response);
        } else {
            res.json({ success: true, data: response });
        }
    } catch (error) {
        console.error('移除监听失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.post('/listeners/clear', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/listeners/clear', { method: 'POST' });

        // 广播监听列表变化
        broadcastToWeChatClients({
            type: 'listeners_changed',
            data: { action: 'clear' }
        });

        if (response.success !== undefined) {
            res.json(response);
        } else {
            res.json({ success: true, data: response });
        }
    } catch (error) {
        console.error('清空监听列表失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 7. 日志管理
router.get('/logs/list', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/logs/list');
        if (response.success !== undefined) {
            res.json(response);
        } else {
            res.json({ success: true, data: response });
        }
    } catch (error) {
        console.error('获取日志列表失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.get('/logs/read', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/logs/read', {
            params: req.query
        });
        if (response.success !== undefined) {
            res.json(response);
        } else {
            res.json({ success: true, data: response });
        }
    } catch (error) {
        console.error('读取日志失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 8. 获取日志用户列表（用于前端选择）
router.get('/logs/users', async (req, res) => {
    try {
        // 直接调用微信API获取日志列表
        const response = await proxyToWeChatAPI('/api/logs/list');

        if (response.success && response.data) {
            // 转换格式为前端需要的用户列表格式
            const users = [];

            // 处理私聊用户
            if (response.data.private_chats) {
                for (const chat of response.data.private_chats) {
                    users.push({
                        name: chat.user_name,
                        type: 'private',
                        latest_log: chat.files.length > 0 ? chat.files[0].name : null,
                        log_count: chat.file_count
                    });
                }
            }

            // 处理群聊用户
            if (response.data.group_chats) {
                for (const chat of response.data.group_chats) {
                    users.push({
                        name: chat.user_name,
                        type: 'group',
                        latest_log: chat.files.length > 0 ? chat.files[0].name : null,
                        log_count: chat.file_count
                    });
                }
            }

            console.log('日志用户列表获取成功:', users.length, '个用户');
            res.json({ success: true, data: users });
        } else {
            console.error('微信API返回失败:', response);
            res.json({ success: false, error: '获取日志列表失败', data: [] });
        }
    } catch (error) {
        console.error('获取日志用户列表失败:', error);
        res.status(500).json({ success: false, error: error.message, data: [] });
    }
});

// 读取日志内容
router.get('/logs/content/:userName/:fileName', async (req, res) => {
    try {
        const { userName, fileName } = req.params;
        const { type } = req.query; // private 或 group

        console.log('读取日志内容:', { userName, fileName, type });

        // 调用微信API读取日志内容
        const response = await proxyToWeChatAPI(`/api/logs/read?user_name=${encodeURIComponent(userName)}&file_name=${encodeURIComponent(fileName)}&user_type=${type || 'private'}`);

        if (response.success && response.data) {
            // 格式化日志内容为友好格式
            let formattedContent = '';
            const chatType = type === 'group' ? '群聊' : '私聊';

            if (response.data.content) {
                formattedContent = `以下是${chatType}【${userName}】的聊天记录 (${fileName}):\n\n`;
                formattedContent += response.data.content;
            } else {
                formattedContent = `${chatType}【${userName}】的日志文件 ${fileName} 为空或不存在`;
            }

            console.log('日志内容读取成功，长度:', formattedContent.length);
            res.json({
                success: true,
                data: {
                    content: formattedContent,
                    user_name: userName,
                    file_name: fileName,
                    chat_type: chatType,
                    raw_content: response.data.content
                }
            });
        } else {
            console.error('微信API返回失败:', response);
            res.json({
                success: false,
                error: '读取日志内容失败',
                data: { content: `无法读取${type === 'group' ? '群聊' : '私聊'}【${userName}】的日志文件 ${fileName}` }
            });
        }
    } catch (error) {
        console.error('读取日志内容失败:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            data: { content: `读取日志时发生错误: ${error.message}` }
        });
    }
});

// 9. 机器人信息
router.get('/robot/info', async (req, res) => {
    try {
        const data = await proxyToWeChatAPI('/api/robot/info');
        res.json({ success: true, data });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 10. 接收微信消息的webhook端点（给微信适配器调用）
router.post('/webhook/message', (req, res) => {
    try {
        const messageData = req.body;

        // 广播接收到的消息到管理面板
        broadcastToWeChatClients({
            type: 'message_received',
            data: messageData
        });

        // 广播微信事件到AI自动回复系统
        if (webSocketServer) {
            webSocketServer.broadcast({
                type: 'wechat_event',
                timestamp: new Date().toISOString(),
                data: {
                    event_type: 'message',
                    message_type: messageData.message_type || 'private',
                    user_name: messageData.user_name,
                    sender_name: messageData.sender_name,
                    content: messageData.content,
                    raw_content: messageData.raw_content || messageData.content,
                    content_type: messageData.content_type || 'text',
                    timestamp: new Date().toISOString(),
                    file_path: messageData.file_path || null,
                    voice_text: messageData.voice_text || null
                }
            }, 'WeChat');
        }

        console.log('收到微信消息:', messageData);
        res.json({ success: true, message: '消息已接收' });
    } catch (error) {
        console.error('处理微信消息webhook失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 10.5 缓存管理API
router.get('/cache/stats', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/cache/stats');
        res.json(response);
    } catch (error) {
        console.error('获取缓存统计失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.post('/cache/cleanup', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/cache/cleanup', { method: 'POST' });
        res.json(response);
    } catch (error) {
        console.error('清理缓存失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.post('/cache/reset', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/cache/reset', { method: 'POST' });
        res.json(response);
    } catch (error) {
        console.error('重置缓存统计失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

router.get('/cache/status', async (req, res) => {
    try {
        const response = await proxyToWeChatAPI('/api/cache/status');
        res.json(response);
    } catch (error) {
        console.error('获取缓存状态失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 11. 进程级别的启动/停止控制（自动化命令）
const { spawn, exec } = require('child_process');
const fsSync = require('fs');

let wechatProcess = null;

// 启动微信适配器进程
router.post('/process/start', async (req, res) => {
    try {
        if (wechatProcess && !wechatProcess.killed) {
            return res.json({
                success: false,
                error: '微信适配器进程已在运行中',
                data: { pid: wechatProcess.pid }
            });
        }

        // 构建启动命令路径
        const wechatDir = path.join(__dirname, '..', 'WeChat');
        const pythonPath = path.join(wechatDir, 'venv_py311', 'Scripts', 'python.exe');
        const scriptPath = path.join(wechatDir, 'wechat_api.py');

        console.log('启动微信适配器进程...');
        console.log('工作目录:', wechatDir);
        console.log('Python路径:', pythonPath);
        console.log('脚本路径:', scriptPath);

        // 启动进程，设置正确的编码
        wechatProcess = spawn(pythonPath, [scriptPath], {
            cwd: wechatDir,
            detached: false,
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                PYTHONIOENCODING: 'utf-8',
                PYTHONUNBUFFERED: '1',
                LANG: 'zh_CN.UTF-8'
            },
            shell: false,
            windowsHide: true
        });

        // 监听进程事件
        wechatProcess.on('spawn', () => {
            console.log(`微信适配器进程已启动，PID: ${wechatProcess.pid}`);
            broadcastToWeChatClients({
                type: 'process_status_changed',
                data: { status: 'started', pid: wechatProcess.pid }
            });
        });

        wechatProcess.on('error', (error) => {
            console.error('微信适配器进程启动失败:', error);
            wechatProcess = null;
            broadcastToWeChatClients({
                type: 'process_status_changed',
                data: { status: 'error', error: error.message }
            });
        });

        wechatProcess.on('exit', (code, signal) => {
            console.log(`微信适配器进程已退出，代码: ${code}, 信号: ${signal}`);
            wechatProcess = null;
            broadcastToWeChatClients({
                type: 'process_status_changed',
                data: { status: 'stopped', code, signal }
            });
        });

        // 处理输出，解析JSON事件和日志
        let stdoutBuffer = '';
        let stderrBuffer = '';

        wechatProcess.stdout.on('data', (data) => {
            try {
                const output = data.toString('utf8');
                stdoutBuffer += output;

                // 检查是否包含完整的事件数据
                const eventStartIndex = stdoutBuffer.indexOf('WECHAT_EVENT_START');
                const eventEndIndex = stdoutBuffer.indexOf('WECHAT_EVENT_END');

                if (eventStartIndex !== -1 && eventEndIndex !== -1) {
                    // 提取JSON事件数据
                    const jsonStart = eventStartIndex + 'WECHAT_EVENT_START'.length;
                    const jsonData = stdoutBuffer.substring(jsonStart, eventEndIndex);

                    try {
                        const eventData = JSON.parse(jsonData);
                        console.log('微信事件:', eventData);

                        // 广播结构化的事件数据
                        broadcastToWeChatClients({
                            type: 'wechat_message',
                            timestamp: new Date().toISOString(),
                            data: eventData.data
                        });

                    } catch (parseError) {
                        console.error('解析微信事件JSON失败:', parseError);
                    }

                    // 清理已处理的数据
                    stdoutBuffer = stdoutBuffer.substring(eventEndIndex + 'WECHAT_EVENT_END'.length);
                } else {
                    // 处理普通输出
                    const lines = stdoutBuffer.split('\n');
                    for (let i = 0; i < lines.length - 1; i++) {
                        const line = lines[i];
                        if (line.trim() && !line.includes('WECHAT_EVENT_')) {
                            console.log('微信适配器输出:', line);
                        }
                    }
                    // 保留最后一行（可能不完整）
                    stdoutBuffer = lines[lines.length - 1];
                }

            } catch (error) {
                console.error('处理stdout输出时出错:', error);
            }
        });

        wechatProcess.stderr.on('data', (data) => {
            try {
                const output = data.toString('utf8');
                stderrBuffer += output;

                // 处理stderr日志，过滤掉不重要的信息
                const lines = stderrBuffer.split('\n');
                for (let i = 0; i < lines.length - 1; i++) {
                    const line = lines[i];
                    if (line.trim() &&
                        !line.includes('[werkzeug]') &&
                        !line.includes('127.0.0.1') &&
                        !line.includes('WARNING: This is a development server')) {
                        console.log('微信适配器日志:', line);
                    }
                }
                // 保留最后一行（可能不完整）
                stderrBuffer = lines[lines.length - 1];

            } catch (error) {
                console.error('处理stderr输出时出错:', error);
            }
        });

        // 等待一段时间确认启动成功
        setTimeout(() => {
            if (wechatProcess && !wechatProcess.killed) {
                res.json({
                    success: true,
                    message: '微信适配器进程启动成功',
                    data: {
                        pid: wechatProcess.pid,
                        command: `${pythonPath} ${scriptPath}`,
                        workingDirectory: wechatDir
                    }
                });
            } else {
                res.status(500).json({
                    success: false,
                    error: '微信适配器进程启动失败或已退出'
                });
            }
        }, 2000);

    } catch (error) {
        console.error('启动微信适配器进程失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 停止微信适配器进程
router.post('/process/stop', async (req, res) => {
    try {
        if (!wechatProcess || wechatProcess.killed) {
            return res.json({
                success: false,
                error: '微信适配器进程未在运行'
            });
        }

        console.log(`正在停止微信适配器进程，PID: ${wechatProcess.pid}`);

        // 尝试优雅关闭
        wechatProcess.kill('SIGTERM');

        // 等待进程退出
        setTimeout(() => {
            if (wechatProcess && !wechatProcess.killed) {
                console.log('强制终止微信适配器进程');
                wechatProcess.kill('SIGKILL');
            }
        }, 5000);

        res.json({
            success: true,
            message: '微信适配器进程停止命令已发送',
            data: { pid: wechatProcess.pid }
        });

    } catch (error) {
        console.error('停止微信适配器进程失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 获取进程状态
router.get('/process/status', async (req, res) => {
    try {
        const isRunning = wechatProcess && !wechatProcess.killed;
        res.json({
            success: true,
            data: {
                running: isRunning,
                pid: isRunning ? wechatProcess.pid : null,
                startTime: isRunning ? wechatProcess.spawnargs : null
            }
        });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 12. 虚拟环境管理接口

// 辅助函数：检测可用的Python版本
async function detectPythonVersions() {
    const pythonCommands = [
        'python3.12',
        'py -3.12',
        'python312',
        'python3.11',
        'py -3.11',
        'python311',
        'python3.10',
        'py -3.10',
        'python310',
        'python3.9',
        'py -3.9',
        'python39',
        'python3',
        'python',
        'py'
    ];

    const availableVersions = [];

    for (const cmd of pythonCommands) {
        try {
            const version = await new Promise((resolve) => {
                exec(`${cmd} --version`, (error, stdout, stderr) => {
                    if (error) {
                        resolve(null);
                        return;
                    }
                    const output = stdout || stderr;
                    const match = output.match(/Python (\d+\.\d+\.\d+)/);
                    if (match) {
                        resolve({
                            command: cmd,
                            version: match[1],
                            majorMinor: match[1].split('.').slice(0, 2).join('.')
                        });
                    } else {
                        resolve(null);
                    }
                });
            });

            if (version) {
                availableVersions.push(version);
            }
        } catch (error) {
            // 忽略错误，继续检测下一个
        }
    }

    return availableVersions;
}

// 辅助函数：选择最佳的Python版本
function selectBestPython(availableVersions) {
    // 优先级：3.12 > 3.11 > 3.10 > 3.9 > 其他
    const priorities = ['3.12', '3.11', '3.10', '3.9', '3.8'];

    for (const priority of priorities) {
        const found = availableVersions.find(v => v.majorMinor === priority);
        if (found) {
            return found;
        }
    }

    // 如果没有找到优先版本，返回第一个可用的
    return availableVersions.length > 0 ? availableVersions[0] : null;
}

// 辅助函数：根据Python版本选择合适的wxautox_wechatbot wheel文件
function selectWxautoxWheel(pythonVersion, libsDir) {
    try {
        // 从Python版本提取主要版本号（如 3.11 -> 311, 3.9 -> 39）
        const versionParts = pythonVersion.split('.');
        const majorMinor = versionParts.slice(0, 2).join('');
        const cpVersion = `cp${majorMinor}`;

        // 读取libs目录中的文件
        const files = fsSync.readdirSync(libsDir);

        // 查找匹配的wxautox_wechatbot wheel文件
        const wheelPattern = new RegExp(`wxautox_wechatbot-.*-${cpVersion}-${cpVersion}-win_amd64\\.whl$`);
        const matchingWheel = files.find(file => wheelPattern.test(file));

        if (matchingWheel) {
            return `./libs/${matchingWheel}`;
        }

        // 如果没有找到精确匹配，尝试查找其他可用版本
        const allWheels = files.filter(file => file.startsWith('wxautox_wechatbot') && file.endsWith('.whl'));
        console.log(`未找到Python ${pythonVersion}的wxautox_wechatbot wheel文件`);
        console.log('可用的wheel文件:', allWheels);

        // 返回第一个可用的wheel文件作为后备
        if (allWheels.length > 0) {
            console.log(`使用后备wheel文件: ${allWheels[0]}`);
            return `./libs/${allWheels[0]}`;
        }

        return null;
    } catch (error) {
        console.error('选择wxautox_wechatbot wheel文件时出错:', error);
        return null;
    }
}

// 辅助函数：生成动态的requirements.txt内容（仅替换wxautox_wechatbot wheel文件）
function generateDynamicRequirements(pythonVersion, wechatDir) {
    try {
        const requirementsPath = path.join(wechatDir, 'requirements.txt');
        const libsDir = path.join(wechatDir, 'libs');

        // 读取原始requirements.txt
        let content = fsSync.readFileSync(requirementsPath, 'utf8');

        // 选择合适的wxautox_wechatbot wheel文件
        const wheelPath = selectWxautoxWheel(pythonVersion, libsDir);

        if (wheelPath) {
            // 替换wxautox_wechatbot行
            content = content.replace(
                /^\.\/libs\/wxautox_wechatbot-.*\.whl$/m,
                wheelPath
            );
            console.log(`为Python ${pythonVersion}选择wheel文件: ${wheelPath}`);
        } else {
            console.warn(`未找到适合Python ${pythonVersion}的wxautox_wechatbot wheel文件`);
        }

        return content;
    } catch (error) {
        console.error('生成动态requirements.txt时出错:', error);
        return null;
    }
}

// 检测可用的Python版本
router.get('/environment/python-versions', async (req, res) => {
    try {
        console.log('检测可用的Python版本...');
        const availableVersions = await detectPythonVersions();

        if (availableVersions.length === 0) {
            return res.json({
                success: false,
                error: '未找到可用的Python版本',
                data: {
                    availableVersions: [],
                    recommendedVersion: null,
                    selectedVersion: null
                }
            });
        }

        const selectedPython = selectBestPython(availableVersions);

        res.json({
            success: true,
            data: {
                availableVersions: availableVersions.map(v => ({
                    command: v.command,
                    version: v.version,
                    majorMinor: v.majorMinor,
                    isRecommended: v.majorMinor === '3.12'
                })),
                recommendedVersion: '3.12',
                selectedVersion: {
                    command: selectedPython.command,
                    version: selectedPython.version,
                    majorMinor: selectedPython.majorMinor,
                    isRecommended: selectedPython.majorMinor === '3.12'
                }
            }
        });
    } catch (error) {
        console.error('检测Python版本失败:', error);
        res.status(500).json({
            success: false,
            error: error.message,
            data: {
                availableVersions: [],
                recommendedVersion: '3.11',
                selectedVersion: null
            }
        });
    }
});

// 检查虚拟环境状态
router.get('/environment/status', async (req, res) => {
    try {
        const wechatDir = path.join(__dirname, '..', 'WeChat');
        const venvDir = path.join(wechatDir, 'venv_py311');
        const pythonPath = path.join(venvDir, 'Scripts', 'python.exe');
        const pipPath = path.join(venvDir, 'Scripts', 'pip.exe');
        const pyvenvCfgPath = path.join(venvDir, 'pyvenv.cfg');
        const requirementsPath = path.join(wechatDir, 'requirements.txt');
        const libsDir = path.join(wechatDir, 'libs');

        // 基础检查
        const venvExists = fsSync.existsSync(venvDir);
        const pythonExists = fsSync.existsSync(pythonPath);
        const pipExists = fsSync.existsSync(pipPath);
        const configExists = fsSync.existsSync(pyvenvCfgPath);
        const requirementsExists = fsSync.existsSync(requirementsPath);
        const libsDirExists = fsSync.existsSync(libsDir);

        let issues = [];
        let configContent = null;
        let isPortable = false;
        let dependencyStatus = {};

        // 检查基础文件
        if (!venvExists) {
            issues.push('虚拟环境目录不存在 (venv_py311)');
        }
        if (!pythonExists) {
            issues.push('Python解释器不存在 (venv_py311/Scripts/python.exe)');
        }
        if (!pipExists) {
            issues.push('pip工具不存在 (venv_py311/Scripts/pip.exe)');
        }
        if (!requirementsExists) {
            issues.push('依赖文件不存在 (requirements.txt)');
        }
        if (!libsDirExists) {
            issues.push('本地依赖包目录不存在 (libs)');
        }

        // 检查配置文件（不再检查硬编码路径问题）
        if (configExists) {
            try {
                configContent = fsSync.readFileSync(pyvenvCfgPath, 'utf8');
                isPortable = true; // 假设环境是可移植的
            } catch (error) {
                issues.push('无法读取虚拟环境配置文件');
                console.error('读取pyvenv.cfg失败:', error);
            }
        }

        // 检查关键依赖包
        if (venvExists && pythonExists) {
            try {
                // 定义依赖包和对应的导入名称
                const keyDependencies = [
                    { package: 'flask', importName: 'flask' },
                    { package: 'flask-cors', importName: 'flask_cors' },
                    { package: 'requests', importName: 'requests' },
                    { package: 'psutil', importName: 'psutil' },
                    { package: 'pywin32', importName: 'win32api' }, // pywin32的导入名称是win32api
                    { package: 'wxautox-wechatbot', importName: 'wxautox_wechatbot' }
                ];

                for (const dep of keyDependencies) {
                    try {
                        const result = await new Promise((resolve) => {
                            exec(`"${pythonPath}" -c "import ${dep.importName}"`, (error) => {
                                resolve(!error);
                            });
                        });
                        dependencyStatus[dep.package] = result;
                        if (!result) {
                            issues.push(`缺少关键依赖包: ${dep.package}`);
                        }
                    } catch (error) {
                        dependencyStatus[dep.package] = false;
                        issues.push(`无法检查依赖包: ${dep.package}`);
                    }
                }
            } catch (error) {
                issues.push('无法检查依赖包状态');
            }
        }

        // 检查wxautox_wechatbot本地包
        if (libsDirExists) {
            const wxautoxFiles = fsSync.readdirSync(libsDir).filter(file =>
                file.startsWith('wxautox_wechatbot') && file.endsWith('.whl')
            );
            if (wxautoxFiles.length === 0) {
                issues.push('缺少wxautox_wechatbot本地安装包 (libs目录)');
            }
        }

        // 确定整体状态
        let status = 'unknown';
        let statusText = '未知状态';
        let canStart = false;

        if (issues.length === 0) {
            status = 'healthy';
            statusText = '环境正常';
            canStart = true;
        } else if (venvExists && pythonExists && Object.values(dependencyStatus).every(v => v)) {
            // 虚拟环境存在，Python可用，所有依赖都安装了，但有其他轻微问题
            status = 'minor_issues';
            statusText = '轻微问题';
            canStart = true;
        } else if (venvExists && pythonExists) {
            // 虚拟环境存在，Python可用，但缺少依赖
            status = 'broken';
            statusText = '环境损坏';
            canStart = false;
        } else if (venvExists) {
            // 虚拟环境存在但Python不可用
            status = 'broken';
            statusText = '环境损坏';
            canStart = false;
        } else {
            // 虚拟环境不存在
            status = 'missing';
            statusText = '环境缺失';
            canStart = false;
        }

        res.json({
            success: true,
            data: {
                status,
                statusText,
                canStart,
                issues,
                venvExists,
                pythonExists,
                pipExists,
                configExists,
                requirementsExists,
                libsDirExists,
                isPortable,
                dependencyStatus,
                venvPath: venvDir,
                pythonPath,
                configPath: pyvenvCfgPath,
                configContent: configContent ? configContent.substring(0, 500) : null // 限制长度
            }
        });
    } catch (error) {
        console.error('检查虚拟环境状态失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 创建/重建虚拟环境
router.post('/environment/create', async (req, res) => {
    try {
        const wechatDir = path.join(__dirname, '..', 'WeChat');
        const venvDir = path.join(wechatDir, 'venv_py311');
        const requirementsPath = path.join(wechatDir, 'requirements.txt');

        console.log('开始创建虚拟环境...');
        console.log('目标目录:', venvDir);

        // 检测可用的Python版本
        console.log('检测可用的Python版本...');
        const availableVersions = await detectPythonVersions();

        if (availableVersions.length === 0) {
            return res.status(500).json({
                success: false,
                error: '未找到可用的Python版本，请确保已安装Python 3.11或更高版本'
            });
        }

        // 选择最佳的Python版本
        const selectedPython = selectBestPython(availableVersions);
        console.log('检测到的Python版本:', availableVersions.map(v => `${v.command} (${v.version})`));
        console.log('选择的Python版本:', `${selectedPython.command} (${selectedPython.version})`);

        // 检查是否是推荐版本
        if (selectedPython.majorMinor !== '3.12') {
            console.warn(`警告: 使用的是Python ${selectedPython.version}，推荐使用Python 3.12以获得最佳兼容性`);
        }

        // 如果虚拟环境已存在，先删除
        if (fsSync.existsSync(venvDir)) {
            console.log('删除现有虚拟环境...');
            await new Promise((resolve) => {
                exec(`rmdir /s /q "${venvDir}"`, (error) => {
                    if (error) {
                        console.warn('删除虚拟环境警告:', error.message);
                    }
                    resolve();
                });
            });

            // 等待删除完成
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 创建新的虚拟环境，使用选定的Python版本
        console.log('创建新虚拟环境...');
        const createCommand = `${selectedPython.command} -m venv "${venvDir}"`;
        console.log('执行命令:', createCommand);

        await new Promise((resolve, reject) => {
            exec(createCommand, { cwd: wechatDir }, (error, stdout, stderr) => {
                if (error) {
                    console.error('创建虚拟环境失败:', error);
                    console.error('错误输出:', stderr);
                    reject(new Error(`创建虚拟环境失败: ${error.message}\n错误详情: ${stderr}`));
                    return;
                }
                console.log('虚拟环境创建成功');
                if (stdout) console.log('创建输出:', stdout);
                resolve();
            });
        });

        // 升级pip
        console.log('升级pip...');
        const pythonPath = path.join(venvDir, 'Scripts', 'python.exe');
        await new Promise((resolve, reject) => {
            exec(`"${pythonPath}" -m pip install --upgrade pip`, (error, stdout, stderr) => {
                if (error) {
                    console.warn('升级pip警告:', error.message);
                }
                resolve();
            });
        });

        // 安装依赖
        if (fsSync.existsSync(requirementsPath)) {
            console.log('安装依赖包...');

            // 生成适合当前Python版本的requirements内容（仅替换wxautox_wechatbot wheel文件）
            const dynamicRequirements = generateDynamicRequirements(selectedPython.version, wechatDir);

            if (dynamicRequirements) {
                // 创建临时的requirements文件
                const tempRequirementsPath = path.join(wechatDir, 'requirements_temp.txt');
                fsSync.writeFileSync(tempRequirementsPath, dynamicRequirements, 'utf8');

                console.log(`为Python ${selectedPython.version}生成了动态requirements文件`);

                try {
                    await new Promise((resolve, reject) => {
                        exec(`"${pythonPath}" -m pip install -r "${tempRequirementsPath}"`, { cwd: wechatDir }, (error, stdout, stderr) => {
                            if (error) {
                                console.error('安装依赖失败:', error);
                                console.error('错误输出:', stderr);
                                reject(new Error(`安装依赖失败: ${error.message}`));
                                return;
                            }
                            console.log('依赖安装完成');
                            console.log('安装输出:', stdout);
                            resolve();
                        });
                    });
                } finally {
                    // 清理临时文件
                    try {
                        fsSync.unlinkSync(tempRequirementsPath);
                        console.log('已清理临时requirements文件');
                    } catch (cleanupError) {
                        console.warn('清理临时文件失败:', cleanupError.message);
                    }
                }
            } else {
                // 如果动态生成失败，使用原始requirements文件
                console.warn('动态requirements生成失败，使用原始requirements文件');
                await new Promise((resolve, reject) => {
                    exec(`"${pythonPath}" -m pip install -r "${requirementsPath}"`, { cwd: wechatDir }, (error, stdout, stderr) => {
                        if (error) {
                            console.error('安装依赖失败:', error);
                            console.error('错误输出:', stderr);
                            reject(new Error(`安装依赖失败: ${error.message}`));
                            return;
                        }
                        console.log('依赖安装完成');
                        console.log('安装输出:', stdout);
                        resolve();
                    });
                });
            }

            // 特别检查和处理pywin32安装
            console.log('检查pywin32安装状态...');
            try {
                await new Promise((resolve, reject) => {
                    exec(`"${pythonPath}" -c "import win32api; print('pywin32 OK')"`, (error, stdout, stderr) => {
                        if (error) {
                            console.log('pywin32未正确安装，尝试重新安装...');
                            // 尝试重新安装pywin32
                            exec(`"${pythonPath}" -m pip install --force-reinstall pywin32`, (error2, stdout2, stderr2) => {
                                if (error2) {
                                    console.warn('pywin32重新安装失败:', error2.message);
                                    console.warn('错误输出:', stderr2);
                                } else {
                                    console.log('pywin32重新安装成功');
                                    console.log('安装输出:', stdout2);
                                }
                                resolve(); // 不管成功失败都继续
                            });
                        } else {
                            console.log('pywin32安装正常');
                            resolve();
                        }
                    });
                });
            } catch (error) {
                console.warn('检查pywin32时出错:', error.message);
            }
        } else {
            console.log('未找到requirements.txt，跳过依赖安装');
        }

        res.json({
            success: true,
            message: '虚拟环境创建成功',
            data: {
                venvPath: venvDir,
                pythonPath,
                hasRequirements: fsSync.existsSync(requirementsPath),
                selectedPython: {
                    command: selectedPython.command,
                    version: selectedPython.version,
                    majorMinor: selectedPython.majorMinor
                },
                availableVersions: availableVersions.map(v => ({
                    command: v.command,
                    version: v.version,
                    majorMinor: v.majorMinor
                })),
                isRecommendedVersion: selectedPython.majorMinor === '3.12'
            }
        });

    } catch (error) {
        console.error('创建虚拟环境失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// 删除虚拟环境
router.delete('/environment/delete', async (req, res) => {
    try {
        const wechatDir = path.join(__dirname, '..', 'WeChat');
        const venvDir = path.join(wechatDir, 'venv_py311');

        if (!fsSync.existsSync(venvDir)) {
            return res.json({
                success: false,
                error: '虚拟环境不存在'
            });
        }

        console.log('删除虚拟环境:', venvDir);

        await new Promise((resolve, reject) => {
            exec(`rmdir /s /q "${venvDir}"`, (error) => {
                if (error) {
                    console.error('删除虚拟环境失败:', error);
                    reject(new Error(`删除虚拟环境失败: ${error.message}`));
                    return;
                }
                console.log('虚拟环境删除成功');
                resolve();
            });
        });

        res.json({
            success: true,
            message: '虚拟环境删除成功'
        });

    } catch (error) {
        console.error('删除虚拟环境失败:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

module.exports = { router, initializeWebSocket, broadcastToWeChatClients };