#!/usr/bin/env node

// Chrome浏览器自动化插件测试脚本
const { spawn } = require('child_process');
const path = require('path');

// 测试用例
const testCases = [
    {
        name: '启动Chrome浏览器',
        input: { action: 'start' }
    },
    {
        name: '导航到Google',
        input: { action: 'navigate', url: 'https://www.google.com' }
    },
    {
        name: '获取页面标题',
        input: { action: 'gettitle' }
    },
    {
        name: '获取当前URL',
        input: { action: 'geturl' }
    },
    {
        name: '截图',
        input: { action: 'screenshot', filename: 'test_screenshot.png' }
    },
    {
        name: '关闭Chrome浏览器',
        input: { action: 'close' }
    }
];

// 执行单个测试
async function runTest(testCase) {
    return new Promise((resolve, reject) => {
        console.log(`\n🧪 测试: ${testCase.name}`);
        console.log(`📝 输入: ${JSON.stringify(testCase.input)}`);

        const child = spawn('node', ['ChromeAutomation.js'], {
            cwd: __dirname,
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        child.stdout.on('data', (data) => {
            stdout += data.toString();
        });

        child.stderr.on('data', (data) => {
            stderr += data.toString();
        });

        child.on('close', (code) => {
            try {
                if (stdout.trim()) {
                    const result = JSON.parse(stdout.trim());
                    if (result.success) {
                        console.log(`✅ 成功: ${result.message}`);
                        if (result.data) {
                            console.log(`📊 数据: ${JSON.stringify(result.data).substring(0, 100)}...`);
                        }
                    } else {
                        console.log(`❌ 失败: ${result.error}`);
                    }
                    resolve(result);
                } else {
                    console.log(`❌ 无输出，退出代码: ${code}`);
                    if (stderr) {
                        console.log(`🔍 错误信息: ${stderr}`);
                    }
                    resolve({ success: false, error: '无输出' });
                }
            } catch (e) {
                console.log(`❌ 解析输出失败: ${e.message}`);
                console.log(`📄 原始输出: ${stdout}`);
                resolve({ success: false, error: '解析失败' });
            }
        });

        child.on('error', (error) => {
            console.log(`❌ 进程错误: ${error.message}`);
            reject(error);
        });

        // 发送输入
        child.stdin.write(JSON.stringify(testCase.input));
        child.stdin.end();
    });
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始Chrome浏览器自动化插件测试');
    console.log('=' * 50);

    let passed = 0;
    let failed = 0;

    for (const testCase of testCases) {
        try {
            const result = await runTest(testCase);
            if (result.success) {
                passed++;
            } else {
                failed++;
            }

            // 在测试之间添加延迟
            await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
            console.log(`❌ 测试异常: ${error.message}`);
            failed++;
        }
    }

    console.log('\n' + '=' * 50);
    console.log(`📊 测试结果: ${passed} 通过, ${failed} 失败`);

    if (failed === 0) {
        console.log('🎉 所有测试通过！');
    } else {
        console.log('⚠️  部分测试失败，请检查配置和环境');
    }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = { runTest, runAllTests };