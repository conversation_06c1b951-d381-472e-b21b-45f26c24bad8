# Chrome浏览器自动化插件配置文件

# Chrome浏览器设置
CHROME_HEADLESS=false
CHROME_PATH=
CHROME_USER_DATA_DIR=
CHROME_WINDOW_SIZE=1920,1080
CHROME_DEBUG_PORT=9222

# 超时设置
CHROME_TIMEOUT=30000
CHROME_WAIT_TIMEOUT=10000

# 截图设置
CHROME_SCREENSHOT_PATH=./screenshots

# 调试模式
DEBUG_MODE=false

# 示例配置说明：
# CHROME_HEADLESS: 是否以无头模式运行Chrome (true/false)
# CHROME_PATH: Chrome可执行文件路径，留空自动检测
# CHROME_USER_DATA_DIR: Chrome用户数据目录，留空使用临时目录
# CHROME_WINDOW_SIZE: 浏览器窗口大小 (宽度,高度)
# CHROME_DEBUG_PORT: Chrome调试端口
# CHROME_TIMEOUT: 操作超时时间 (毫秒)
# CHROME_WAIT_TIMEOUT: 等待元素超时时间 (毫秒)
# CHROME_SCREENSHOT_PATH: 截图保存路径
# DEBUG_MODE: 是否启用调试模式 (true/false)