/**
 * 完整的前端工作流程测试
 * 模拟用户在前端页面的完整操作流程
 */

const axios = require('axios');

const API_BASE = 'http://localhost:6005/admin_api/wechat';

async function testCompleteFrontendWorkflow() {
    console.log('🧪 开始完整前端工作流程测试...\n');

    try {
        // 1. 模拟页面加载 - 获取配置
        console.log('📖 1. 模拟页面加载 - 获取配置...');
        const initialConfigResponse = await axios.get(`${API_BASE}/config`);
        const initialConfig = initialConfigResponse.data.data;
        
        console.log('✅ 页面加载成功，配置获取完成');
        console.log('📋 初始配置状态:');
        console.log(`   - 私聊回复概率: ${initialConfig.AI_AUTO_REPLY.private_chat.reply_probability}`);
        console.log(`   - 群聊回复概率: ${initialConfig.AI_AUTO_REPLY.group_chat.reply_probability}`);
        console.log(`   - 私聊Assistant ID: ${initialConfig.AI_AUTO_REPLY.private_chat.assistant_id}`);
        console.log(`   - 群聊Assistant ID: ${initialConfig.AI_AUTO_REPLY.group_chat.assistant_id}`);

        // 2. 模拟用户修改私聊配置
        console.log('\n👤 2. 模拟用户修改私聊配置...');
        const userModifiedConfig = JSON.parse(JSON.stringify(initialConfig));
        
        // 用户在前端表单中修改的值
        userModifiedConfig.AI_AUTO_REPLY.private_chat.reply_probability = 0.9;
        userModifiedConfig.AI_AUTO_REPLY.private_chat.assistant_id = 1234567890;
        userModifiedConfig.AI_AUTO_REPLY.private_chat.user_id = 9876543210;
        userModifiedConfig.AI_AUTO_REPLY.private_chat.assistantName = '新助手名称';
        userModifiedConfig.AI_AUTO_REPLY.private_chat.model = 'gpt-4o-mini';
        
        console.log('🔧 用户修改的私聊配置:');
        console.log(`   - 回复概率: ${userModifiedConfig.AI_AUTO_REPLY.private_chat.reply_probability}`);
        console.log(`   - Assistant ID: ${userModifiedConfig.AI_AUTO_REPLY.private_chat.assistant_id}`);
        console.log(`   - User ID: ${userModifiedConfig.AI_AUTO_REPLY.private_chat.user_id}`);
        console.log(`   - 助手名称: ${userModifiedConfig.AI_AUTO_REPLY.private_chat.assistantName}`);
        console.log(`   - 模型: ${userModifiedConfig.AI_AUTO_REPLY.private_chat.model}`);

        // 3. 模拟前端保存操作
        console.log('\n💾 3. 模拟前端保存操作...');
        const saveResponse = await axios.post(`${API_BASE}/config`, userModifiedConfig);
        
        if (saveResponse.data.success) {
            console.log('✅ 前端保存请求成功');
        } else {
            console.log('❌ 前端保存请求失败:', saveResponse.data.error);
            return;
        }

        // 4. 模拟页面刷新 - 重新获取配置验证
        console.log('\n🔄 4. 模拟页面刷新 - 重新获取配置验证...');
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待保存完成
        
        const refreshedConfigResponse = await axios.get(`${API_BASE}/config`);
        const refreshedConfig = refreshedConfigResponse.data.data;
        
        console.log('📋 刷新后的配置状态:');
        console.log(`   - 私聊回复概率: ${refreshedConfig.AI_AUTO_REPLY.private_chat.reply_probability}`);
        console.log(`   - 私聊Assistant ID: ${refreshedConfig.AI_AUTO_REPLY.private_chat.assistant_id}`);
        console.log(`   - 私聊User ID: ${refreshedConfig.AI_AUTO_REPLY.private_chat.user_id}`);
        console.log(`   - 私聊助手名称: ${refreshedConfig.AI_AUTO_REPLY.private_chat.assistantName}`);
        console.log(`   - 私聊模型: ${refreshedConfig.AI_AUTO_REPLY.private_chat.model}`);

        // 5. 验证修改是否生效
        console.log('\n✅ 5. 验证修改是否生效...');
        const verificationResults = {
            reply_probability: refreshedConfig.AI_AUTO_REPLY.private_chat.reply_probability === 0.9,
            assistant_id: refreshedConfig.AI_AUTO_REPLY.private_chat.assistant_id === 1234567890,
            user_id: refreshedConfig.AI_AUTO_REPLY.private_chat.user_id === 9876543210,
            assistantName: refreshedConfig.AI_AUTO_REPLY.private_chat.assistantName === '新助手名称',
            model: refreshedConfig.AI_AUTO_REPLY.private_chat.model === 'gpt-4o-mini'
        };

        let allVerified = true;
        for (const [field, isCorrect] of Object.entries(verificationResults)) {
            if (isCorrect) {
                console.log(`   ✅ ${field}: 修改生效`);
            } else {
                console.log(`   ❌ ${field}: 修改未生效`);
                allVerified = false;
            }
        }

        // 6. 模拟用户修改群聊配置
        console.log('\n👤 6. 模拟用户修改群聊配置...');
        const groupModifiedConfig = JSON.parse(JSON.stringify(refreshedConfig));
        
        groupModifiedConfig.AI_AUTO_REPLY.group_chat.reply_probability = 0.7;
        groupModifiedConfig.AI_AUTO_REPLY.group_chat.keyword_must_trigger = false;
        groupModifiedConfig.AI_AUTO_REPLY.group_chat.trigger_keywords = ['新关键词', '测试'];
        
        console.log('🔧 用户修改的群聊配置:');
        console.log(`   - 回复概率: ${groupModifiedConfig.AI_AUTO_REPLY.group_chat.reply_probability}`);
        console.log(`   - 关键词必须触发: ${groupModifiedConfig.AI_AUTO_REPLY.group_chat.keyword_must_trigger}`);
        console.log(`   - 触发关键词: [${groupModifiedConfig.AI_AUTO_REPLY.group_chat.trigger_keywords.join(', ')}]`);

        // 7. 保存群聊配置
        console.log('\n💾 7. 保存群聊配置...');
        await axios.post(`${API_BASE}/config`, groupModifiedConfig);
        console.log('✅ 群聊配置保存完成');

        // 8. 验证群聊配置
        console.log('\n✅ 8. 验证群聊配置...');
        const finalConfigResponse = await axios.get(`${API_BASE}/config`);
        const finalConfig = finalConfigResponse.data.data;
        
        const groupVerification = {
            reply_probability: finalConfig.AI_AUTO_REPLY.group_chat.reply_probability === 0.7,
            keyword_must_trigger: finalConfig.AI_AUTO_REPLY.group_chat.keyword_must_trigger === false,
            trigger_keywords: JSON.stringify(finalConfig.AI_AUTO_REPLY.group_chat.trigger_keywords) === JSON.stringify(['新关键词', '测试'])
        };

        let groupVerified = true;
        for (const [field, isCorrect] of Object.entries(groupVerification)) {
            if (isCorrect) {
                console.log(`   ✅ 群聊${field}: 修改生效`);
            } else {
                console.log(`   ❌ 群聊${field}: 修改未生效`);
                groupVerified = false;
            }
        }

        // 9. 恢复原始配置
        console.log('\n🔄 9. 恢复原始配置...');
        await axios.post(`${API_BASE}/config`, initialConfig);
        console.log('✅ 原始配置已恢复');

        // 10. 最终验证
        console.log('\n🔍 10. 最终验证恢复结果...');
        const restoredConfigResponse = await axios.get(`${API_BASE}/config`);
        const restoredConfig = restoredConfigResponse.data.data;
        
        const restorationVerified = 
            restoredConfig.AI_AUTO_REPLY.private_chat.reply_probability === initialConfig.AI_AUTO_REPLY.private_chat.reply_probability &&
            restoredConfig.AI_AUTO_REPLY.group_chat.reply_probability === initialConfig.AI_AUTO_REPLY.group_chat.reply_probability;

        if (restorationVerified) {
            console.log('✅ 配置恢复验证成功');
        } else {
            console.log('❌ 配置恢复验证失败');
        }

        // 11. 最终结果
        console.log('\n🎯 11. 完整工作流程测试结果:');
        
        if (allVerified && groupVerified && restorationVerified) {
            console.log('🎉 完整前端工作流程测试通过！');
            console.log('   ✅ 页面加载配置正常');
            console.log('   ✅ 私聊配置修改生效');
            console.log('   ✅ 群聊配置修改生效');
            console.log('   ✅ 配置保存功能正常');
            console.log('   ✅ 页面刷新后配置持久化');
            console.log('   ✅ 配置恢复功能正常');
            console.log('\n🚀 前端配置系统完全正常工作！');
            console.log('💡 用户可以在前端页面正常修改所有配置项，包括：');
            console.log('   - 私聊/群聊回复概率');
            console.log('   - Assistant ID 和 User ID');
            console.log('   - 助手名称和模型');
            console.log('   - 关键词触发设置');
            console.log('   - 触发关键词列表');
            console.log('   - 所有其他配置项');
        } else {
            console.log('❌ 完整前端工作流程测试失败！');
            if (!allVerified) {
                console.log('   ❌ 私聊配置修改存在问题');
            }
            if (!groupVerified) {
                console.log('   ❌ 群聊配置修改存在问题');
            }
            if (!restorationVerified) {
                console.log('   ❌ 配置恢复存在问题');
            }
        }

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testCompleteFrontendWorkflow();
