/**
 * 测试前端是否还有重复的配置字段
 * 检查HTML表单中是否有重复的name属性
 */

const fs = require('fs');
const path = require('path');

function testFrontendDuplicates() {
    console.log('🧪 开始检查前端重复配置字段...\n');

    try {
        // 读取HTML文件
        const htmlPath = path.join(__dirname, 'AdminPanel', 'index.html');
        const htmlContent = fs.readFileSync(htmlPath, 'utf8');

        // 提取所有的name属性
        const nameRegex = /name="([^"]+)"/g;
        const nameAttributes = [];
        let match;

        while ((match = nameRegex.exec(htmlContent)) !== null) {
            nameAttributes.push({
                name: match[1],
                line: htmlContent.substring(0, match.index).split('\n').length
            });
        }

        console.log(`📋 找到 ${nameAttributes.length} 个name属性`);

        // 检查重复的name属性
        const nameCount = {};
        const duplicates = {};

        nameAttributes.forEach(attr => {
            if (!nameCount[attr.name]) {
                nameCount[attr.name] = [];
            }
            nameCount[attr.name].push(attr.line);
        });

        // 找出重复的字段
        for (const [name, lines] of Object.entries(nameCount)) {
            if (lines.length > 1) {
                duplicates[name] = lines;
            }
        }

        console.log('\n🔍 重复字段检查结果:');
        
        if (Object.keys(duplicates).length === 0) {
            console.log('✅ 没有发现重复的配置字段！');
        } else {
            console.log('❌ 发现重复的配置字段:');
            for (const [name, lines] of Object.entries(duplicates)) {
                console.log(`   - ${name}: 出现在第 ${lines.join(', ')} 行`);
            }
        }

        // 检查特定的关键字段
        console.log('\n🎯 关键字段检查:');
        const keyFields = [
            'AI_AUTO_REPLY.private_chat.reply_probability',
            'AI_AUTO_REPLY.group_chat.reply_probability',
            'AI_AUTO_REPLY.private_chat.assistant_id',
            'AI_AUTO_REPLY.group_chat.assistant_id',
            'AI_AUTO_REPLY.private_chat.user_id',
            'AI_AUTO_REPLY.group_chat.user_id',
            'AI_AUTO_REPLY.private_chat.model',
            'AI_AUTO_REPLY.group_chat.model',
            'AI_AUTO_REPLY.private_chat.assistantName',
            'AI_AUTO_REPLY.group_chat.assistantName'
        ];

        let allKeyFieldsUnique = true;
        for (const field of keyFields) {
            const count = nameCount[field] ? nameCount[field].length : 0;
            if (count === 0) {
                console.log(`   ❌ ${field}: 缺失`);
                allKeyFieldsUnique = false;
            } else if (count === 1) {
                console.log(`   ✅ ${field}: 唯一 (第${nameCount[field][0]}行)`);
            } else {
                console.log(`   ❌ ${field}: 重复 ${count} 次 (第${nameCount[field].join(', ')}行)`);
                allKeyFieldsUnique = false;
            }
        }

        // 检查是否有废弃的字段
        console.log('\n🗑️ 废弃字段检查:');
        const deprecatedFields = [
            'AI_AUTO_REPLY.chat_config',
            'AI_AUTO_REPLY.assistant_id',
            'AI_AUTO_REPLY.user_id',
            'AI_AUTO_REPLY.render_as_image',
            'AI_AUTO_REPLY.useragent'
        ];

        let hasDeprecatedFields = false;
        for (const field of deprecatedFields) {
            const count = nameCount[field] ? nameCount[field].length : 0;
            if (count > 0) {
                console.log(`   ❌ 发现废弃字段: ${field} (第${nameCount[field].join(', ')}行)`);
                hasDeprecatedFields = true;
            } else {
                console.log(`   ✅ 无废弃字段: ${field}`);
            }
        }

        // 统计配置字段分布
        console.log('\n📊 配置字段分布:');
        const fieldCategories = {
            'private_chat': [],
            'group_chat': [],
            'global': [],
            'other': []
        };

        for (const name of Object.keys(nameCount)) {
            if (name.includes('private_chat')) {
                fieldCategories.private_chat.push(name);
            } else if (name.includes('group_chat')) {
                fieldCategories.group_chat.push(name);
            } else if (name.startsWith('AI_AUTO_REPLY.')) {
                fieldCategories.global.push(name);
            } else {
                fieldCategories.other.push(name);
            }
        }

        console.log(`   - 私聊配置字段: ${fieldCategories.private_chat.length} 个`);
        console.log(`   - 群聊配置字段: ${fieldCategories.group_chat.length} 个`);
        console.log(`   - 全局AI配置字段: ${fieldCategories.global.length} 个`);
        console.log(`   - 其他配置字段: ${fieldCategories.other.length} 个`);

        // 最终结果
        console.log('\n🎯 检查结果总结:');
        
        if (Object.keys(duplicates).length === 0 && allKeyFieldsUnique && !hasDeprecatedFields) {
            console.log('🎉 前端配置字段检查通过！');
            console.log('   ✅ 无重复配置字段');
            console.log('   ✅ 关键字段唯一且完整');
            console.log('   ✅ 无废弃字段');
            console.log('\n🚀 前端配置表单现在是一套完整统一的系统！');
        } else {
            console.log('❌ 前端配置字段检查失败！');
            if (Object.keys(duplicates).length > 0) {
                console.log('   ❌ 存在重复配置字段');
            }
            if (!allKeyFieldsUnique) {
                console.log('   ❌ 关键字段不唯一或缺失');
            }
            if (hasDeprecatedFields) {
                console.log('   ❌ 存在废弃字段');
            }
        }

        // 显示详细的私聊和群聊字段
        console.log('\n📝 详细字段列表:');
        console.log('私聊配置字段:');
        fieldCategories.private_chat.forEach(field => {
            console.log(`   - ${field}`);
        });
        
        console.log('\n群聊配置字段:');
        fieldCategories.group_chat.forEach(field => {
            console.log(`   - ${field}`);
        });

    } catch (error) {
        console.error('❌ 检查过程中发生错误:', error.message);
    }
}

// 运行检查
testFrontendDuplicates();
