/**
 * 测试微信适配器配置保存功能
 * 验证私聊概率等配置是否能正确保存和加载
 */

const axios = require('axios');

const API_BASE = 'http://localhost:6005/admin_api/wechat';

async function testConfigSaveAndLoad() {
    console.log('🧪 开始测试微信适配器配置保存功能...\n');

    try {
        // 1. 获取当前配置
        console.log('📖 1. 获取当前配置...');
        const currentConfigResponse = await axios.get(`${API_BASE}/config`);
        console.log('✅ 当前配置获取成功');
        
        const currentConfig = currentConfigResponse.data.data;
        console.log('📋 当前私聊回复概率:', currentConfig.AI_AUTO_REPLY?.private_chat?.reply_probability || '未配置');
        console.log('📋 当前群聊回复概率:', currentConfig.AI_AUTO_REPLY?.group_chat?.reply_probability || '未配置');

        // 2. 准备测试配置（修改私聊概率）
        console.log('\n🔧 2. 准备测试配置...');
        const testConfig = {
            ...currentConfig,
            AI_AUTO_REPLY: {
                ...currentConfig.AI_AUTO_REPLY,
                private_chat: {
                    ...currentConfig.AI_AUTO_REPLY.private_chat,
                    reply_probability: 0.9  // 修改为0.9
                },
                group_chat: {
                    ...currentConfig.AI_AUTO_REPLY.group_chat,
                    reply_probability: 0.5  // 修改为0.5
                }
            }
        };

        console.log('🎯 测试配置 - 私聊回复概率:', testConfig.AI_AUTO_REPLY.private_chat.reply_probability);
        console.log('🎯 测试配置 - 群聊回复概率:', testConfig.AI_AUTO_REPLY.group_chat.reply_probability);

        // 3. 保存测试配置
        console.log('\n💾 3. 保存测试配置...');
        console.log('🔧 发送的配置数据:', JSON.stringify({
            private_probability: testConfig.AI_AUTO_REPLY.private_chat.reply_probability,
            group_probability: testConfig.AI_AUTO_REPLY.group_chat.reply_probability
        }, null, 2));

        const saveResponse = await axios.post(`${API_BASE}/config`, testConfig);
        
        if (saveResponse.data.success) {
            console.log('✅ 配置保存成功');
        } else {
            console.log('❌ 配置保存失败:', saveResponse.data.error);
            return;
        }

        // 4. 重新获取配置验证
        console.log('\n🔍 4. 重新获取配置验证...');
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒确保保存完成
        
        const verifyConfigResponse = await axios.get(`${API_BASE}/config`);
        const verifyConfig = verifyConfigResponse.data.data;
        
        console.log('📋 验证 - 私聊回复概率:', verifyConfig.AI_AUTO_REPLY?.private_chat?.reply_probability);
        console.log('📋 验证 - 群聊回复概率:', verifyConfig.AI_AUTO_REPLY?.group_chat?.reply_probability);

        // 5. 检查是否保存成功
        const privateProbSaved = verifyConfig.AI_AUTO_REPLY?.private_chat?.reply_probability === 0.9;
        const groupProbSaved = verifyConfig.AI_AUTO_REPLY?.group_chat?.reply_probability === 0.5;

        if (privateProbSaved && groupProbSaved) {
            console.log('\n🎉 测试成功！配置保存和加载功能正常工作');
        } else {
            console.log('\n❌ 测试失败！配置未正确保存');
            console.log('   私聊概率保存:', privateProbSaved ? '✅' : '❌');
            console.log('   群聊概率保存:', groupProbSaved ? '✅' : '❌');
        }

        // 6. 恢复原始配置
        console.log('\n🔄 6. 恢复原始配置...');
        await axios.post(`${API_BASE}/config`, currentConfig);
        console.log('✅ 原始配置已恢复');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testConfigSaveAndLoad();
