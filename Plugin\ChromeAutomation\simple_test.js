#!/usr/bin/env node

// 简单的Chrome自动化插件测试
const ChromeAutomation = require('./ChromeAutomation.js');

async function simpleTest() {
    console.log('🚀 开始简单测试...');

    try {
        const automation = new ChromeAutomation();
        console.log('✅ ChromeAutomation实例创建成功');

        // 测试配置加载
        console.log('📋 配置信息:');
        console.log('  - 无头模式:', automation.config.headless);
        console.log('  - 调试端口:', automation.config.port);
        console.log('  - 窗口大小:', automation.config.windowSize);
        console.log('  - Chrome路径:', automation.config.chromePath);

        console.log('✅ 基本测试通过');

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    simpleTest();
}