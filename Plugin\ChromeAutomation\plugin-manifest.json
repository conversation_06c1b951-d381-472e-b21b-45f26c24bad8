{"manifestVersion": "1.0.0", "name": "ChromeAutomation", "displayName": "Chrome浏览器自动化插件", "version": "1.0.0", "description": "基于chrome-remote-interface的浏览器自动化插件，支持页面导航、元素操作、截图、搜索等功能。", "author": "VCPToolBox", "pluginType": "asynchronous", "entryPoint": {"type": "nodejs", "command": "node ChromeAutomation.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "dependencies": {"chrome-remote-interface": "^0.33.0"}, "configSchema": {"CHROME_HEADLESS": {"type": "boolean", "description": "是否以无头模式运行Chrome", "required": false, "default": false}, "CHROME_PATH": {"type": "string", "description": "Chrome可执行文件路径，留空自动检测", "required": false, "default": ""}, "CHROME_USER_DATA_DIR": {"type": "string", "description": "Chrome用户数据目录，留空使用临时目录", "required": false, "default": ""}, "CHROME_WINDOW_SIZE": {"type": "string", "description": "浏览器窗口大小 (宽度,高度)", "required": false, "default": "1920,1080"}, "CHROME_DEBUG_PORT": {"type": "number", "description": "Chrome调试端口", "required": false, "default": 9222, "minimum": 1024, "maximum": 65535}, "CHROME_TIMEOUT": {"type": "number", "description": "操作超时时间 (毫秒)", "required": false, "default": 30000, "minimum": 5000, "maximum": 300000}, "CHROME_WAIT_TIMEOUT": {"type": "number", "description": "等待元素超时时间 (毫秒)", "required": false, "default": 10000, "minimum": 1000, "maximum": 60000}, "CHROME_SCREENSHOT_PATH": {"type": "string", "description": "截图保存路径", "required": false, "default": "./screenshots"}, "DEBUG_MODE": {"type": "boolean", "description": "是否启用调试模式", "required": false, "default": false}}, "capabilities": {"invocationCommands": [{"commandIdentifier": "ChromeAutomation", "description": "Chrome浏览器自动化操作工具。支持启动浏览器、页面导航、元素操作、截图、搜索等功能。请使用以下格式调用：\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」ChromeAutomation「末」,\naction:「始」操作类型「末」,\n[其他参数]\n<<<[END_TOOL_REQUEST]>>>\n\n支持的操作类型：\n- start: 启动Chrome浏览器\n- navigate: 导航到指定URL (需要url参数)\n- click: 点击元素 (需要selector参数)\n- input: 输入文本 (需要selector和text参数)\n- gettext: 获取元素文本 (需要selector参数)\n- screenshot: 截图 (可选format、quality、fullPage参数)\n- search: 搜索网站 (需要searchUrl和query参数)\n- scroll: 滚动页面 (可选direction和distance参数)\n- gettitle: 获取页面标题\n- geturl: 获取当前URL\n- getcontent: 获取页面内容\n- execute: 执行JavaScript代码 (需要script参数)\n- wait: 等待元素出现 (需要selector参数)\n- close: 关闭Chrome浏览器", "example": "```text\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」ChromeAutomation「末」,\naction:「始」navigate「末」,\nurl:「始」https://www.google.com「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}