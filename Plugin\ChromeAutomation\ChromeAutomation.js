#!/usr/bin/env node

const CDP = require('chrome-remote-interface');
const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');
const os = require('os');

// 使用动态导入或者axios作为fetch的替代
let fetch;
try {
    fetch = require('node-fetch');
} catch (e) {
    // 如果没有node-fetch，使用简单的http请求
    const http = require('http');
    fetch = (url) => {
        return new Promise((resolve, reject) => {
            const req = http.get(url, (res) => {
                resolve({ ok: res.statusCode === 200 });
            });
            req.on('error', reject);
        });
    };
}

// 引入VCP日志系统，强制输出到stderr以避免污染stdout的JSON
let logger;
try {
    const loggerPath = process.env.NODE_ENV === 'production'
        ? '../../utils/logger.cjs'
        : '../../utils/logger.js';
    const vcpLogger = require(loggerPath).default || require(loggerPath);

    // 为JSON输出插件创建特殊的logger，强制输出到stderr
    logger = {
        info: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [i] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        error: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [x] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        warning: (component, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [!] [${component}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        },
        debug: (component, msg, data) => {
            if (process.env.DEBUG_MODE === 'true') {
                const output = `[${new Date().toLocaleString('zh-CN')}] [*] [${component}] ${msg}`;
                console.error(output);
                if (data) console.error(data);
            }
        },
        plugin: (name, msg, data) => {
            const output = `[${new Date().toLocaleString('zh-CN')}] [P] [插件-${name}] ${msg}`;
            console.error(output);
            if (data) console.error(data);
        }
    };
} catch (e) {
    // 回退到传统日志
    logger = {
        info: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        warning: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        debug: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
        plugin: (name, msg, data) => console.error(`[插件-${name}] ${msg}`, data || '')
    };
}

class ChromeAutomation {
    constructor() {
        this.client = null;
        this.chromeProcess = null;
        this.port = 9222;
        this.isConnected = false;
        this.currentTabId = null;
        this.config = this.loadConfig();
    }

    // 加载配置
    loadConfig() {
        const defaultConfig = {
            headless: process.env.CHROME_HEADLESS === 'true',
            chromePath: process.env.CHROME_PATH || this.findChromePath(),
            userDataDir: process.env.CHROME_USER_DATA_DIR || path.join(os.tmpdir(), 'chrome-automation'),
            windowSize: process.env.CHROME_WINDOW_SIZE || '1920,1080',
            timeout: parseInt(process.env.CHROME_TIMEOUT) || 30000,
            waitTimeout: parseInt(process.env.CHROME_WAIT_TIMEOUT) || 10000,
            screenshotPath: process.env.CHROME_SCREENSHOT_PATH || path.join(__dirname, 'screenshots'),
            port: parseInt(process.env.CHROME_DEBUG_PORT) || 9222
        };

        this.port = defaultConfig.port;
        return defaultConfig;
    }

    // 查找Chrome可执行文件路径
    findChromePath() {
        const possiblePaths = {
            win32: [
                'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
                'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
                path.join(os.homedir(), 'AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'),
                'chrome.exe' // 尝试从PATH中查找
            ],
            darwin: [
                '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
                '/Applications/Chromium.app/Contents/MacOS/Chromium'
            ],
            linux: [
                '/usr/bin/google-chrome',
                '/usr/bin/google-chrome-stable',
                '/usr/bin/chromium-browser',
                '/usr/bin/chromium'
            ]
        };

        const platform = os.platform();
        const paths = possiblePaths[platform] || possiblePaths.linux;

        for (const chromePath of paths) {
            try {
                if (chromePath === 'chrome.exe') {
                    // 对于Windows，尝试使用where命令查找chrome
                    try {
                        const { execSync } = require('child_process');
                        const result = execSync('where chrome', { encoding: 'utf8' });
                        if (result.trim()) {
                            return result.trim().split('\n')[0];
                        }
                    } catch (e) {
                        // where命令失败，继续尝试其他路径
                    }
                } else {
                    require('fs').accessSync(chromePath, require('fs').constants.F_OK);
                    return chromePath;
                }
            } catch (e) {
                // 继续尝试下一个路径
            }
        }

        // 如果都找不到，返回一个默认值并在启动时报错
        logger.warning('ChromeAutomation', '未找到Chrome浏览器，将使用默认路径');
        return platform === 'win32' ? 'chrome.exe' : 'google-chrome';
    }

    // 启动Chrome浏览器
    async startChrome() {
        if (this.chromeProcess) {
            logger.warning('ChromeAutomation', 'Chrome进程已存在');
            return;
        }

        const args = [
            `--remote-debugging-port=${this.port}`,
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            `--user-data-dir=${this.config.userDataDir}`,
            `--window-size=${this.config.windowSize}`
        ];

        if (this.config.headless) {
            args.push('--headless');
        }

        logger.info('ChromeAutomation', `启动Chrome: ${this.config.chromePath}`);
        logger.debug('ChromeAutomation', `Chrome参数: ${args.join(' ')}`);

        this.chromeProcess = spawn(this.config.chromePath, args, {
            stdio: 'pipe',
            detached: false
        });

        this.chromeProcess.on('error', (error) => {
            logger.error('ChromeAutomation', `Chrome启动失败: ${error.message}`);
        });

        this.chromeProcess.on('exit', (code) => {
            logger.info('ChromeAutomation', `Chrome进程退出，代码: ${code}`);
            this.chromeProcess = null;
            this.isConnected = false;
        });

        // 等待Chrome启动
        await this.waitForChrome();
    }

    // 等待Chrome启动完成
    async waitForChrome() {
        const maxAttempts = 30;
        let attempts = 0;

        while (attempts < maxAttempts) {
            try {
                const response = await fetch(`http://localhost:${this.port}/json/version`);
                if (response.ok) {
                    logger.info('ChromeAutomation', 'Chrome启动完成');
                    return;
                }
            } catch (e) {
                // Chrome还未启动完成
            }

            attempts++;
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        throw new Error('Chrome启动超时');
    }

    // 连接到Chrome
    async connect() {
        if (this.isConnected) {
            return;
        }

        try {
            this.client = await CDP({ port: this.port });
            const { Network, Page, Runtime, DOM } = this.client;

            await Network.enable();
            await Page.enable();
            await Runtime.enable();
            await DOM.enable();

            this.isConnected = true;
            logger.info('ChromeAutomation', 'Chrome连接成功');

        } catch (error) {
            logger.error('ChromeAutomation', `Chrome连接失败: ${error.message}`);
            throw error;
        }
    }

    // 断开连接
    async disconnect() {
        if (this.client) {
            await this.client.close();
            this.client = null;
            this.isConnected = false;
            logger.info('ChromeAutomation', 'Chrome连接已断开');
        }
    }

    // 关闭Chrome
    async closeChrome() {
        await this.disconnect();

        if (this.chromeProcess) {
            this.chromeProcess.kill('SIGTERM');

            // 等待进程退出
            await new Promise((resolve) => {
                this.chromeProcess.on('exit', resolve);
                setTimeout(() => {
                    if (this.chromeProcess) {
                        this.chromeProcess.kill('SIGKILL');
                    }
                    resolve();
                }, 5000);
            });

            this.chromeProcess = null;
        }
    }

    // 导航到URL
    async navigateToUrl(url) {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        logger.info('ChromeAutomation', `导航到: ${url}`);

        const { Page } = this.client;
        await Page.navigate({ url });
        await Page.loadEventFired();

        logger.info('ChromeAutomation', '页面加载完成');
    }

    // 等待元素出现
    async waitForElement(selector, timeout = this.config.waitTimeout) {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        const { Runtime } = this.client;
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            try {
                const result = await Runtime.evaluate({
                    expression: `document.querySelector('${selector}') !== null`
                });

                if (result.result.value) {
                    return true;
                }
            } catch (e) {
                // 继续等待
            }

            await new Promise(resolve => setTimeout(resolve, 100));
        }

        throw new Error(`元素 ${selector} 在 ${timeout}ms 内未出现`);
    }

    // 点击元素
    async clickElement(selector) {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        await this.waitForElement(selector);

        const { Runtime } = this.client;
        const result = await Runtime.evaluate({
            expression: `
                const element = document.querySelector('${selector}');
                if (element) {
                    element.click();
                    true;
                } else {
                    false;
                }
            `
        });

        if (!result.result.value) {
            throw new Error(`无法点击元素: ${selector}`);
        }

        logger.info('ChromeAutomation', `已点击元素: ${selector}`);
    }

    // 输入文本
    async inputText(selector, text) {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        await this.waitForElement(selector);

        const { Runtime } = this.client;
        const result = await Runtime.evaluate({
            expression: `
                const element = document.querySelector('${selector}');
                if (element) {
                    element.focus();
                    element.value = '${text.replace(/'/g, "\\'")}';
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    true;
                } else {
                    false;
                }
            `
        });

        if (!result.result.value) {
            throw new Error(`无法在元素中输入文本: ${selector}`);
        }

        logger.info('ChromeAutomation', `已在 ${selector} 中输入文本`);
    }

    // 获取元素文本
    async getElementText(selector) {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        await this.waitForElement(selector);

        const { Runtime } = this.client;
        const result = await Runtime.evaluate({
            expression: `
                const element = document.querySelector('${selector}');
                element ? element.textContent.trim() : null;
            `
        });

        return result.result.value;
    }

    // 获取页面内容
    async getPageContent() {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        const { Runtime } = this.client;
        const result = await Runtime.evaluate({
            expression: 'document.documentElement.outerHTML'
        });

        return result.result.value;
    }

    // 截图
    async takeScreenshot(options = {}) {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        const { Page } = this.client;

        // 确保截图目录存在
        await fs.mkdir(this.config.screenshotPath, { recursive: true });

        const screenshotOptions = {
            format: options.format || 'png',
            quality: options.quality || 90,
            fullPage: options.fullPage !== false,
            clip: options.clip
        };

        const screenshot = await Page.captureScreenshot(screenshotOptions);

        const filename = options.filename || `screenshot_${Date.now()}.${screenshotOptions.format}`;
        const filepath = path.join(this.config.screenshotPath, filename);

        await fs.writeFile(filepath, screenshot.data, 'base64');

        logger.info('ChromeAutomation', `截图已保存: ${filepath}`);

        return {
            filepath,
            filename,
            data: screenshot.data
        };
    }

    // 搜索网站
    async searchWebsite(searchUrl, query, searchSelector = 'input[type="search"], input[name="q"], input[name="query"]', submitSelector = 'button[type="submit"], input[type="submit"]') {
        await this.navigateToUrl(searchUrl);
        await this.inputText(searchSelector, query);

        // 尝试点击搜索按钮，如果没有找到就按回车
        try {
            await this.clickElement(submitSelector);
        } catch (e) {
            // 如果没有找到提交按钮，尝试按回车键
            const { Runtime } = this.client;
            await Runtime.evaluate({
                expression: `
                    const element = document.querySelector('${searchSelector}');
                    if (element) {
                        element.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', keyCode: 13 }));
                    }
                `
            });
        }

        // 等待搜索结果加载
        await new Promise(resolve => setTimeout(resolve, 2000));

        logger.info('ChromeAutomation', `搜索完成: ${query}`);
    }

    // 滚动页面
    async scrollPage(direction = 'down', distance = 500) {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        const { Runtime } = this.client;

        let scrollExpression;
        switch (direction.toLowerCase()) {
            case 'down':
                scrollExpression = `window.scrollBy(0, ${distance})`;
                break;
            case 'up':
                scrollExpression = `window.scrollBy(0, -${distance})`;
                break;
            case 'top':
                scrollExpression = 'window.scrollTo(0, 0)';
                break;
            case 'bottom':
                scrollExpression = 'window.scrollTo(0, document.body.scrollHeight)';
                break;
            default:
                throw new Error(`不支持的滚动方向: ${direction}`);
        }

        await Runtime.evaluate({ expression: scrollExpression });

        // 等待滚动完成
        await new Promise(resolve => setTimeout(resolve, 500));

        logger.info('ChromeAutomation', `页面滚动: ${direction}`);
    }

    // 获取页面标题
    async getPageTitle() {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        const { Runtime } = this.client;
        const result = await Runtime.evaluate({
            expression: 'document.title'
        });

        return result.result.value;
    }

    // 获取当前URL
    async getCurrentUrl() {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        const { Runtime } = this.client;
        const result = await Runtime.evaluate({
            expression: 'window.location.href'
        });

        return result.result.value;
    }

    // 等待页面加载完成
    async waitForPageLoad(timeout = this.config.timeout) {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        const { Runtime } = this.client;
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            try {
                const result = await Runtime.evaluate({
                    expression: 'document.readyState === "complete"'
                });

                if (result.result.value) {
                    logger.info('ChromeAutomation', '页面加载完成');
                    return;
                }
            } catch (e) {
                // 继续等待
            }

            await new Promise(resolve => setTimeout(resolve, 100));
        }

        throw new Error('页面加载超时');
    }

    // 执行JavaScript代码
    async executeScript(script) {
        if (!this.isConnected) {
            throw new Error('Chrome未连接');
        }

        const { Runtime } = this.client;
        const result = await Runtime.evaluate({
            expression: script,
            returnByValue: true
        });

        if (result.exceptionDetails) {
            throw new Error(`脚本执行失败: ${result.exceptionDetails.text}`);
        }

        return result.result.value;
    }
}

// 主要的执行函数
async function executeAction(automation, action, params) {
    try {
        switch (action.toLowerCase()) {
            case 'start':
                await automation.startChrome();
                await automation.connect();
                return { success: true, message: 'Chrome已启动并连接' };

            case 'navigate':
                if (!params.url) throw new Error('缺少URL参数');
                await automation.navigateToUrl(params.url);
                return { success: true, message: `已导航到: ${params.url}` };

            case 'click':
                if (!params.selector) throw new Error('缺少选择器参数');
                await automation.clickElement(params.selector);
                return { success: true, message: `已点击元素: ${params.selector}` };

            case 'input':
                if (!params.selector || !params.text) throw new Error('缺少选择器或文本参数');
                await automation.inputText(params.selector, params.text);
                return { success: true, message: `已在 ${params.selector} 中输入文本` };

            case 'gettext':
                if (!params.selector) throw new Error('缺少选择器参数');
                const text = await automation.getElementText(params.selector);
                return { success: true, data: text, message: '已获取元素文本' };

            case 'screenshot':
                const screenshot = await automation.takeScreenshot(params);
                return { success: true, data: screenshot, message: '截图完成' };

            case 'search':
                if (!params.searchUrl || !params.query) throw new Error('缺少搜索URL或查询参数');
                await automation.searchWebsite(
                    params.searchUrl,
                    params.query,
                    params.searchSelector,
                    params.submitSelector
                );
                return { success: true, message: `搜索完成: ${params.query}` };

            case 'scroll':
                await automation.scrollPage(params.direction, params.distance);
                return { success: true, message: `页面滚动: ${params.direction || 'down'}` };

            case 'gettitle':
                const title = await automation.getPageTitle();
                return { success: true, data: title, message: '已获取页面标题' };

            case 'geturl':
                const url = await automation.getCurrentUrl();
                return { success: true, data: url, message: '已获取当前URL' };

            case 'getcontent':
                const content = await automation.getPageContent();
                return { success: true, data: content, message: '已获取页面内容' };

            case 'execute':
                if (!params.script) throw new Error('缺少脚本参数');
                const result = await automation.executeScript(params.script);
                return { success: true, data: result, message: '脚本执行完成' };

            case 'wait':
                if (!params.selector) throw new Error('缺少选择器参数');
                await automation.waitForElement(params.selector, params.timeout);
                return { success: true, message: `元素 ${params.selector} 已出现` };

            case 'close':
                await automation.closeChrome();
                return { success: true, message: 'Chrome已关闭' };

            default:
                throw new Error(`不支持的操作: ${action}`);
        }
    } catch (error) {
        logger.error('ChromeAutomation', `操作失败: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 主函数
async function main() {
    try {
        // 读取stdin输入
        let inputData = '';

        if (process.stdin.isTTY) {
            // 如果是TTY模式，从命令行参数读取
            const args = process.argv.slice(2);
            if (args.length === 0) {
                throw new Error('请提供操作参数');
            }
            inputData = args.join(' ');
        } else {
            // 从stdin读取
            for await (const chunk of process.stdin) {
                inputData += chunk;
            }
        }

        if (!inputData.trim()) {
            throw new Error('未提供输入数据');
        }

        // 解析输入参数
        let params;
        try {
            params = JSON.parse(inputData.trim());
        } catch (e) {
            // 如果不是JSON，尝试解析为简单的key=value格式
            params = {};
            const pairs = inputData.trim().split(',');
            for (const pair of pairs) {
                const [key, value] = pair.split('=');
                if (key && value) {
                    params[key.trim()] = value.trim();
                }
            }
        }

        if (!params.action) {
            throw new Error('缺少action参数');
        }

        logger.info('ChromeAutomation', `执行操作: ${params.action}`, params);

        const automation = new ChromeAutomation();
        const result = await executeAction(automation, params.action, params);

        // 输出结果到stdout
        console.log(JSON.stringify(result, null, 2));

    } catch (error) {
        logger.error('ChromeAutomation', `主函数执行失败: ${error.message}`);

        const errorResult = {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };

        console.log(JSON.stringify(errorResult, null, 2));
        process.exit(1);
    }
}

// 如果直接运行此文件，执行主函数
if (require.main === module) {
    main().catch(error => {
        logger.error('ChromeAutomation', `未捕获的错误: ${error.message}`);
        process.exit(1);
    });
}

module.exports = ChromeAutomation;